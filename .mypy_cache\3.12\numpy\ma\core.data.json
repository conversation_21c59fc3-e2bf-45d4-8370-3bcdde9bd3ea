{".class": "MypyFile", "_fullname": "numpy.ma.core", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MAError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.ma.core.MA<PERSON>rror", "name": "MAError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MA<PERSON>rror", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.ma.core", "mro": ["numpy.ma.core.MA<PERSON>rror", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core.MAError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.ma.core.MA<PERSON>rror", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MaskError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy.ma.core.MA<PERSON>rror"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.ma.core.MaskError", "name": "Mask<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.ma.core", "mro": ["numpy.ma.core.MaskError", "numpy.ma.core.MA<PERSON>rror", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core.MaskError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.ma.core.MaskError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MaskType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy.ma.core.MaskType", "line": 28, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "numpy.bool_"}}, "MaskedArray": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._ShapeType", "id": 1, "name": "_ShapeType", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._DType_co", "id": 2, "name": "_DType_co", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.ma.core.MaskedArray", "name": "Masked<PERSON><PERSON><PERSON>", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._ShapeType", "id": 1, "name": "_ShapeType", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._DType_co", "id": 2, "name": "_DType_co", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.ma.core", "mro": ["numpy.ma.core.MaskedArray", "numpy.n<PERSON><PERSON>", "numpy._ArrayOrScalarCommon", "builtins.object"], "names": {".class": "SymbolTable", "T": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedArray.T", "name": "T", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "__add__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__add__", "name": "__add__", "type": null}}, "__array_finalize__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__array_finalize__", "name": "__array_finalize__", "type": null}}, "__array_priority__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedArray.__array_priority__", "name": "__array_priority__", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "__array_wrap__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "obj", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__array_wrap__", "name": "__array_wrap__", "type": null}}, "__deepcopy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "memo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__deepcopy__", "name": "__deepcopy__", "type": null}}, "__div__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__div__", "name": "__div__", "type": null}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__eq__", "name": "__eq__", "type": null}}, "__float__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__float__", "name": "__float__", "type": null}}, "__floordiv__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__floordiv__", "name": "__floordiv__", "type": null}}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__ge__", "name": "__ge__", "type": null}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__getitem__", "name": "__getitem__", "type": null}}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__gt__", "name": "__gt__", "type": null}}, "__iadd__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__iadd__", "name": "__iadd__", "type": null}}, "__idiv__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__idiv__", "name": "__idiv__", "type": null}}, "__ifloordiv__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__ifloordiv__", "name": "__ifloordiv__", "type": null}}, "__imul__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__imul__", "name": "__imul__", "type": null}}, "__int__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__int__", "name": "__int__", "type": null}}, "__ipow__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__ipow__", "name": "__ipow__", "type": null}}, "__isub__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__isub__", "name": "__isub__", "type": null}}, "__itruediv__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__itruediv__", "name": "__itruediv__", "type": null}}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__le__", "name": "__le__", "type": null}}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__lt__", "name": "__lt__", "type": null}}, "__mul__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__mul__", "name": "__mul__", "type": null}}, "__ne__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__ne__", "name": "__ne__", "type": null}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["cls", "data", "mask", "dtype", "copy", "subok", "ndmin", "fill_value", "keep_mask", "hard_mask", "shrink", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "numpy.ma.core.MaskedArray.__new__", "name": "__new__", "type": null}}, "__pow__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__pow__", "name": "__pow__", "type": null}}, "__radd__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__radd__", "name": "__radd__", "type": null}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__reduce__", "name": "__reduce__", "type": null}}, "__rfloordiv__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__rfloordiv__", "name": "__rfloordiv__", "type": null}}, "__rmul__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__rmul__", "name": "__rmul__", "type": null}}, "__rpow__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__rpow__", "name": "__rpow__", "type": null}}, "__rsub__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__rsub__", "name": "__rsub__", "type": null}}, "__rtruediv__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__rtruediv__", "name": "__rtruediv__", "type": null}}, "__setitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__setitem__", "name": "__setitem__", "type": null}}, "__setmask__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "mask", "copy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__setmask__", "name": "__setmask__", "type": null}}, "__sub__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__sub__", "name": "__sub__", "type": null}}, "__truediv__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.__truediv__", "name": "__truediv__", "type": null}}, "all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "axis", "out", "keepdims"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.all", "name": "all", "type": null}}, "anom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "axis", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.anom", "name": "anom", "type": null}}, "any": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "axis", "out", "keepdims"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.any", "name": "any", "type": null}}, "argmax": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 5], "arg_names": ["self", "axis", "fill_value", "out", "keepdims"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.argmax", "name": "argmax", "type": null}}, "argmin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 5], "arg_names": ["self", "axis", "fill_value", "out", "keepdims"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.argmin", "name": "a<PERSON><PERSON>", "type": null}}, "argpartition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.argpartition", "name": "argpartition", "type": null}}, "argsort": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "axis", "kind", "order", "endwith", "fill_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.argsort", "name": "argsor<PERSON>", "type": null}}, "baseclass": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.ma.core.MaskedArray.baseclass", "name": "baseclass", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.ma.core.MaskedArray.baseclass", "name": "baseclass", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._ShapeType", "id": 1, "name": "_ShapeType", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._DType_co", "id": 2, "name": "_DType_co", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.ma.core.MaskedArray"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "baseclass of MaskedArray", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "compress": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "condition", "axis", "out"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.compress", "name": "compress", "type": null}}, "compressed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.compressed", "name": "compressed", "type": null}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedArray.copy", "name": "copy", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "axis", "keepdims"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.count", "name": "count", "type": null}}, "cumprod": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "axis", "dtype", "out"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.cumprod", "name": "cump<PERSON>", "type": null}}, "cumsum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "axis", "dtype", "out"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.cumsum", "name": "cumsum", "type": null}}, "data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedArray.data", "name": "data", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "diagonal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedArray.diagonal", "name": "diagonal", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "dot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "b", "out", "strict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.dot", "name": "dot", "type": null}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "numpy.ma.core.MaskedArray.dtype", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "numpy.ma.core.MaskedArray.dtype", "name": "dtype", "type": null}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "numpy.ma.core.MaskedArray.dtype", "name": "dtype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._ShapeType", "id": 1, "name": "_ShapeType", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._DType_co", "id": 2, "name": "_DType_co", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.ma.core.MaskedArray"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtype of MaskedArray", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "numpy.ma.core.MaskedArray.dtype", "name": "dtype", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "dtype", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._ShapeType", "id": 1, "name": "_ShapeType", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._DType_co", "id": 2, "name": "_DType_co", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.ma.core.MaskedArray"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dtype", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "fill_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "numpy.ma.core.MaskedArray.fill_value", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "numpy.ma.core.MaskedArray.fill_value", "name": "fill_value", "type": null}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "numpy.ma.core.MaskedArray.fill_value", "name": "fill_value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._ShapeType", "id": 1, "name": "_ShapeType", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._DType_co", "id": 2, "name": "_DType_co", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.ma.core.MaskedArray"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fill_value of MaskedArray", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "numpy.ma.core.MaskedArray.fill_value", "name": "fill_value", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "fill_value", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._ShapeType", "id": 1, "name": "_ShapeType", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._DType_co", "id": 2, "name": "_DType_co", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.ma.core.MaskedArray"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fill_value", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "filled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "fill_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.filled", "name": "filled", "type": null}}, "flat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "numpy.ma.core.MaskedArray.flat", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "numpy.ma.core.MaskedArray.flat", "name": "flat", "type": null}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "numpy.ma.core.MaskedArray.flat", "name": "flat", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._ShapeType", "id": 1, "name": "_ShapeType", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._DType_co", "id": 2, "name": "_DType_co", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.ma.core.MaskedArray"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flat of MaskedArray", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "numpy.ma.core.MaskedArray.flat", "name": "flat", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "flat", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._ShapeType", "id": 1, "name": "_ShapeType", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._DType_co", "id": 2, "name": "_DType_co", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.ma.core.MaskedArray"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flat", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "flatten": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedArray.flatten", "name": "flatten", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "get_fill_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedArray.get_fill_value", "name": "get_fill_value", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "get_imag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedArray.get_imag", "name": "get_imag", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "get_real": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedArray.get_real", "name": "get_real", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "harden_mask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.harden_mask", "name": "harden_mask", "type": null}}, "hardmask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.ma.core.MaskedArray.hardmask", "name": "hardmask", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.ma.core.MaskedArray.hardmask", "name": "hardmask", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._ShapeType", "id": 1, "name": "_ShapeType", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._DType_co", "id": 2, "name": "_DType_co", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.ma.core.MaskedArray"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hardmask of MaskedArray", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ids": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.ids", "name": "ids", "type": null}}, "imag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.ma.core.MaskedArray.imag", "name": "imag", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.ma.core.MaskedArray.imag", "name": "imag", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._ShapeType", "id": 1, "name": "_ShapeType", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._DType_co", "id": 2, "name": "_DType_co", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.ma.core.MaskedArray"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "imag of MaskedArray", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "iscontiguous": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.iscontiguous", "name": "iscontiguous", "type": null}}, "mask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "numpy.ma.core.MaskedArray.mask", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "numpy.ma.core.MaskedArray.mask", "name": "mask", "type": null}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "numpy.ma.core.MaskedArray.mask", "name": "mask", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._ShapeType", "id": 1, "name": "_ShapeType", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._DType_co", "id": 2, "name": "_DType_co", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.ma.core.MaskedArray"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mask of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "numpy.ma.core.MaskedArray.mask", "name": "mask", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "mask", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._ShapeType", "id": 1, "name": "_ShapeType", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._DType_co", "id": 2, "name": "_DType_co", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.ma.core.MaskedArray"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mask", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "max": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "axis", "out", "fill_value", "keepdims"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.max", "name": "max", "type": null}}, "mean": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "axis", "dtype", "out", "keepdims"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.mean", "name": "mean", "type": null}}, "min": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "axis", "out", "fill_value", "keepdims"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.min", "name": "min", "type": null}}, "nonzero": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.nonzero", "name": "nonzero", "type": null}}, "partition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.partition", "name": "partition", "type": null}}, "prod": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "axis", "dtype", "out", "keepdims"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.prod", "name": "prod", "type": null}}, "product": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedArray.product", "name": "product", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "ptp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "axis", "out", "fill_value", "keepdims"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.ptp", "name": "ptp", "type": null}}, "put": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "indices", "values", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.put", "name": "put", "type": null}}, "ravel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.ravel", "name": "ravel", "type": null}}, "real": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.ma.core.MaskedArray.real", "name": "real", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.ma.core.MaskedArray.real", "name": "real", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._ShapeType", "id": 1, "name": "_ShapeType", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._DType_co", "id": 2, "name": "_DType_co", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.ma.core.MaskedArray"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "real of MaskedArray", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "recordmask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "numpy.ma.core.MaskedArray.recordmask", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "numpy.ma.core.MaskedArray.recordmask", "name": "recordmask", "type": null}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "numpy.ma.core.MaskedArray.recordmask", "name": "recordmask", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._ShapeType", "id": 1, "name": "_ShapeType", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._DType_co", "id": 2, "name": "_DType_co", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.ma.core.MaskedArray"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "recordmask of MaskedArray", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "numpy.ma.core.MaskedArray.recordmask", "name": "recordmask", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "recordmask", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._ShapeType", "id": 1, "name": "_ShapeType", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._DType_co", "id": 2, "name": "_DType_co", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.ma.core.MaskedArray"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "recordmask", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "repeat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedArray.repeat", "name": "repeat", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "reshape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "s", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.reshape", "name": "reshape", "type": null}}, "resize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "newshape", "refcheck", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.resize", "name": "resize", "type": null}}, "round": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "decimals", "out"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.round", "name": "round", "type": null}}, "set_fill_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedArray.set_fill_value", "name": "set_fill_value", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "shape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "numpy.ma.core.MaskedArray.shape", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "numpy.ma.core.MaskedArray.shape", "name": "shape", "type": null}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "numpy.ma.core.MaskedArray.shape", "name": "shape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._ShapeType", "id": 1, "name": "_ShapeType", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._DType_co", "id": 2, "name": "_DType_co", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.ma.core.MaskedArray"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shape of MaskedArray", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "numpy.ma.core.MaskedArray.shape", "name": "shape", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "shape", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._ShapeType", "id": 1, "name": "_ShapeType", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._DType_co", "id": 2, "name": "_DType_co", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.ma.core.MaskedArray"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shape", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "sharedmask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.ma.core.MaskedArray.sharedmask", "name": "sharedmask", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.ma.core.MaskedArray.sharedmask", "name": "sharedmask", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._ShapeType", "id": 1, "name": "_ShapeType", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._DType_co", "id": 2, "name": "_DType_co", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.ma.core.MaskedArray"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sharedmask of MaskedArray", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "shrink_mask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.shrink_mask", "name": "shrink_mask", "type": null}}, "soften_mask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.soften_mask", "name": "soften_mask", "type": null}}, "sort": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "axis", "kind", "order", "endwith", "fill_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.sort", "name": "sort", "type": null}}, "squeeze": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedArray.squeeze", "name": "squeeze", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "std": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "axis", "dtype", "out", "ddof", "keepdims"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.std", "name": "std", "type": null}}, "sum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "axis", "dtype", "out", "keepdims"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.sum", "name": "sum", "type": null}}, "swapaxes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedArray.swapaxes", "name": "swapaxes", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "take": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "indices", "axis", "out", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.take", "name": "take", "type": null}}, "tobytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "fill_value", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.tobytes", "name": "tobytes", "type": null}}, "tofile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "fid", "sep", "format"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.tofile", "name": "tofile", "type": null}}, "toflex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.toflex", "name": "toflex", "type": null}}, "tolist": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "fill_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.tolist", "name": "tolist", "type": null}}, "torecords": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedArray.torecords", "name": "torecords", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "trace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "offset", "axis1", "axis2", "dtype", "out"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.trace", "name": "trace", "type": null}}, "transpose": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedArray.transpose", "name": "transpose", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "unshare_mask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.unshare_mask", "name": "unshare_mask", "type": null}}, "var": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "axis", "dtype", "out", "ddof", "keepdims"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.var", "name": "var", "type": null}}, "view": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "dtype", "type", "fill_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArray.view", "name": "view", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core.MaskedArray.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._ShapeType", "id": 1, "name": "_ShapeType", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._DType_co", "id": 2, "name": "_DType_co", "namespace": "numpy.ma.core.MaskedArray", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.ma.core.MaskedArray"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ShapeType", "_DType_co"], "typeddict_type": null}}, "MaskedArrayFutureWarning": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.FutureWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.ma.core.MaskedArrayFutureWarning", "name": "MaskedArrayFutureWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedArrayFutureWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.ma.core", "mro": ["numpy.ma.core.MaskedArrayFutureWarning", "builtins.FutureWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core.MaskedArrayFutureWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.ma.core.MaskedArrayFutureWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MaskedConstant": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.ma.core.MaskedArray"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.ma.core.MaskedConstant", "name": "MaskedConstant", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedConstant", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.ma.core", "mro": ["numpy.ma.core.MaskedConstant", "numpy.ma.core.MaskedArray", "numpy.n<PERSON><PERSON>", "numpy._ArrayOrScalarCommon", "builtins.object"], "names": {".class": "SymbolTable", "__array_finalize__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedConstant.__array_finalize__", "name": "__array_finalize__", "type": null}}, "__array_prepare__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "obj", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedConstant.__array_prepare__", "name": "__array_prepare__", "type": null}}, "__array_wrap__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "obj", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedConstant.__array_wrap__", "name": "__array_wrap__", "type": null}}, "__class__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedConstant.__class__", "name": "__class__", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "__copy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedConstant.__copy__", "name": "__copy__", "type": null}}, "__deepcopy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "memo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedConstant.__deepcopy__", "name": "__deepcopy__", "type": null}}, "__format__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "format_spec"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedConstant.__format__", "name": "__format__", "type": null}}, "__iadd__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedConstant.__iadd__", "name": "__iadd__", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "__ifloordiv__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedConstant.__ifloordiv__", "name": "__ifloordiv__", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "__imul__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedConstant.__imul__", "name": "__imul__", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "__iop__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedConstant.__iop__", "name": "__iop__", "type": null}}, "__ipow__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedConstant.__ipow__", "name": "__ipow__", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "__isub__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedConstant.__isub__", "name": "__isub__", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "__itruediv__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedConstant.__itruediv__", "name": "__itruediv__", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "numpy.ma.core.MaskedConstant.__new__", "name": "__new__", "type": null}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedConstant.__reduce__", "name": "__reduce__", "type": null}}, "__setattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "attr", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedConstant.__setattr__", "name": "__setattr__", "type": null}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedConstant.copy", "name": "copy", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core.MaskedConstant.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.ma.core.MaskedConstant", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MaskedIterator": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.ma.core.MaskedIterator", "name": "MaskedIterator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedIterator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.ma.core", "mro": ["numpy.ma.core.MaskedIterator", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedIterator.__getitem__", "name": "__getitem__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ma"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedIterator.__init__", "name": "__init__", "type": null}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedIterator.__iter__", "name": "__iter__", "type": null}}, "__next__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedIterator.__next__", "name": "__next__", "type": null}}, "__setitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.MaskedIterator.__setitem__", "name": "__setitem__", "type": null}}, "dataiter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedIterator.dataiter", "name": "dataiter", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "ma": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedIterator.ma", "name": "ma", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "maskiter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core.MaskedIterator.maskiter", "name": "maskiter", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core.MaskedIterator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.ma.core.MaskedIterator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_DType_co": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._DType_co", "name": "_DType_co", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}}, "_DomainedBinaryOperation": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy.ma.core._MaskedUFunc"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.ma.core._DomainedBinaryOperation", "name": "_DomainedBinaryOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.ma.core._DomainedBinaryOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.ma.core", "mro": ["numpy.ma.core._DomainedBinaryOperation", "numpy.ma.core._MaskedUFunc", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["self", "a", "b", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core._DomainedBinaryOperation.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "dbfunc", "domain", "fillx", "filly"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core._DomainedBinaryOperation.__init__", "name": "__init__", "type": null}}, "domain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core._DomainedBinaryOperation.domain", "name": "domain", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "fillx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core._DomainedBinaryOperation.fillx", "name": "fillx", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "filly": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core._DomainedBinaryOperation.filly", "name": "filly", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._DomainedBinaryOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.ma.core._DomainedBinaryOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_MaskedBinaryOperation": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy.ma.core._MaskedUFunc"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.ma.core._MaskedBinaryOperation", "name": "_MaskedBinaryOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.ma.core._MaskedBinaryOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.ma.core", "mro": ["numpy.ma.core._MaskedBinaryOperation", "numpy.ma.core._MaskedUFunc", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["self", "a", "b", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core._MaskedBinaryOperation.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "mbfunc", "fillx", "filly"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core._MaskedBinaryOperation.__init__", "name": "__init__", "type": null}}, "accumulate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "target", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core._MaskedBinaryOperation.accumulate", "name": "accumulate", "type": null}}, "fillx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core._MaskedBinaryOperation.fillx", "name": "fillx", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "filly": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core._MaskedBinaryOperation.filly", "name": "filly", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "outer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "a", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core._MaskedBinaryOperation.outer", "name": "outer", "type": null}}, "reduce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "target", "axis", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core._MaskedBinaryOperation.reduce", "name": "reduce", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._MaskedBinaryOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.ma.core._MaskedBinaryOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_MaskedPrintOption": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.ma.core._MaskedPrintOption", "name": "_MaskedPrintOption", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.ma.core._MaskedPrintOption", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.ma.core", "mro": ["numpy.ma.core._MaskedPrintOption", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "display"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core._MaskedPrintOption.__init__", "name": "__init__", "type": null}}, "display": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core._MaskedPrintOption.display", "name": "display", "type": null}}, "enable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "shrink"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core._MaskedPrintOption.enable", "name": "enable", "type": null}}, "enabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core._MaskedPrintOption.enabled", "name": "enabled", "type": null}}, "set_display": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core._MaskedPrintOption.set_display", "name": "set_display", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._MaskedPrintOption.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.ma.core._MaskedPrintOption", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_MaskedUFunc": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.ma.core._MaskedUFunc", "name": "_MaskedUFunc", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.ma.core._MaskedUFunc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.ma.core", "mro": ["numpy.ma.core._MaskedUFunc", "builtins.object"], "names": {".class": "SymbolTable", "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core._MaskedUFunc.__doc__", "name": "__doc__", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ufunc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core._MaskedUFunc.__init__", "name": "__init__", "type": null}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core._MaskedUFunc.__name__", "name": "__name__", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "f": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core._MaskedUFunc.f", "name": "f", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._MaskedUFunc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.ma.core._MaskedUFunc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_MaskedUnaryOperation": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy.ma.core._MaskedUFunc"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.ma.core._MaskedUnaryOperation", "name": "_MaskedUnaryOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.ma.core._MaskedUnaryOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.ma.core", "mro": ["numpy.ma.core._MaskedUnaryOperation", "numpy.ma.core._MaskedUFunc", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "a", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core._MaskedUnaryOperation.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "mufunc", "fill", "domain"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core._MaskedUnaryOperation.__init__", "name": "__init__", "type": null}}, "domain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core._MaskedUnaryOperation.domain", "name": "domain", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "fill": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core._MaskedUnaryOperation.fill", "name": "fill", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._MaskedUnaryOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.ma.core._MaskedUnaryOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ShapeType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._ShapeType", "name": "_ShapeType", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_convert2ma": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.ma.core._convert2ma", "name": "_convert2ma", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.ma.core._convert2ma", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.ma.core", "mro": ["numpy.ma.core._convert2ma", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core._convert2ma.__call__", "name": "__call__", "type": null}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core._convert2ma.__doc__", "name": "__doc__", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "funcname", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core._convert2ma.__init__", "name": "__init__", "type": null}}, "getdoc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core._convert2ma.getdoc", "name": "getdoc", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._convert2ma.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.ma.core._convert2ma", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_extrema_operation": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy.ma.core._MaskedUFunc"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.ma.core._extrema_operation", "name": "_extrema_operation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.ma.core._extrema_operation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.ma.core", "mro": ["numpy.ma.core._extrema_operation", "numpy.ma.core._MaskedUFunc", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "a", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core._extrema_operation.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "ufunc", "compare", "fill_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core._extrema_operation.__init__", "name": "__init__", "type": null}}, "compare": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core._extrema_operation.compare", "name": "compare", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "fill_value_func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core._extrema_operation.fill_value_func", "name": "fill_value_func", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "outer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "a", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core._extrema_operation.outer", "name": "outer", "type": null}}, "reduce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "target", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core._extrema_operation.reduce", "name": "reduce", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._extrema_operation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.ma.core._extrema_operation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_frommethod": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.ma.core._frommethod", "name": "_frommethod", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.ma.core._frommethod", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.ma.core", "mro": ["numpy.ma.core._frommethod", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "a", "args", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core._frommethod.__call__", "name": "__call__", "type": null}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core._frommethod.__doc__", "name": "__doc__", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "methodname", "reversed"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core._frommethod.__init__", "name": "__init__", "type": null}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core._frommethod.__name__", "name": "__name__", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "getdoc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core._frommethod.getdoc", "name": "getdoc", "type": null}}, "reversed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.ma.core._frommethod.reversed", "name": "reversed", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._frommethod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.ma.core._frommethod", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "abs": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.abs", "name": "abs", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "absolute": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.absolute", "name": "absolute", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "add": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.add", "name": "add", "type": "numpy.ma.core._MaskedBinaryOperation"}}, "all": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.all", "name": "all", "type": "numpy.ma.core._frommethod"}}, "allclose": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["a", "b", "masked_equal", "rtol", "atol"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.allclose", "name": "allclose", "type": null}}, "allequal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "fill_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.allequal", "name": "allequal", "type": null}}, "alltrue": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.alltrue", "name": "alltrue", "type": "numpy.ma.core._MaskedBinaryOperation"}}, "amax": {".class": "SymbolTableNode", "cross_ref": "numpy.core.fromnumeric.amax", "kind": "Gdef", "module_public": false}, "amin": {".class": "SymbolTableNode", "cross_ref": "numpy.core.fromnumeric.amin", "kind": "Gdef", "module_public": false}, "angle": {".class": "SymbolTableNode", "cross_ref": "numpy.lib.function_base.angle", "kind": "Gdef", "module_public": false}, "anom": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.anom", "name": "anom", "type": "numpy.ma.core._frommethod"}}, "anomalies": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.anomalies", "name": "anomalies", "type": "numpy.ma.core._frommethod"}}, "any": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.any", "name": "any", "type": "numpy.ma.core._frommethod"}}, "append": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.append", "name": "append", "type": null}}, "arange": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.arange", "name": "arange", "type": "numpy.ma.core._convert2ma"}}, "arccos": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.arccos", "name": "arccos", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "arccosh": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.arccosh", "name": "arccosh", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "arcsin": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.arcsin", "name": "arcsin", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "arcsinh": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.arcsinh", "name": "arcsinh", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "arctan": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.arctan", "name": "arctan", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "arctan2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.arctan2", "name": "arctan2", "type": "numpy.ma.core._MaskedBinaryOperation"}}, "arctanh": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.arctanh", "name": "arctanh", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "argmax": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.argmax", "name": "argmax", "type": "numpy.ma.core._frommethod"}}, "argmin": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.argmin", "name": "a<PERSON><PERSON>", "type": "numpy.ma.core._frommethod"}}, "argsort": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["a", "axis", "kind", "order", "endwith", "fill_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.argsort", "name": "argsor<PERSON>", "type": null}}, "around": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.around", "name": "around", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "array": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["data", "dtype", "copy", "order", "mask", "fill_value", "keep_mask", "hard_mask", "shrink", "subok", "ndmin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.array", "name": "array", "type": null}}, "asanyarray": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.asanyarray", "name": "asany<PERSON>y", "type": null}}, "asarray": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["a", "dtype", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.asarray", "name": "asarray", "type": null}}, "bitwise_and": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.bitwise_and", "name": "bitwise_and", "type": "numpy.ma.core._MaskedBinaryOperation"}}, "bitwise_or": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.bitwise_or", "name": "bitwise_or", "type": "numpy.ma.core._MaskedBinaryOperation"}}, "bitwise_xor": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.bitwise_xor", "name": "bitwise_xor", "type": "numpy.ma.core._MaskedBinaryOperation"}}, "bool_": {".class": "SymbolTableNode", "cross_ref": "numpy.bool_", "kind": "Gdef", "module_public": false}, "ceil": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.ceil", "name": "ceil", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "choose": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["indices", "choices", "out", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.choose", "name": "choose", "type": null}}, "clip": {".class": "SymbolTableNode", "cross_ref": "numpy.core.fromnumeric.clip", "kind": "Gdef", "module_public": false}, "common_fill_value": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.common_fill_value", "name": "common_fill_value", "type": null}}, "compress": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.compress", "name": "compress", "type": "numpy.ma.core._frommethod"}}, "compressed": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.compressed", "name": "compressed", "type": null}}, "concatenate": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["arrays", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.concatenate", "name": "concatenate", "type": null}}, "conjugate": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.conjugate", "name": "conjugate", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "convolve": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "v", "mode", "propagate_mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.convolve", "name": "convolve", "type": null}}, "copy": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.copy", "name": "copy", "type": "numpy.ma.core._frommethod"}}, "correlate": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "v", "mode", "propagate_mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.correlate", "name": "correlate", "type": null}}, "cos": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.cos", "name": "cos", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "cosh": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.cosh", "name": "cosh", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "count": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.count", "name": "count", "type": "numpy.ma.core._frommethod"}}, "cumprod": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.cumprod", "name": "cump<PERSON>", "type": "numpy.ma.core._frommethod"}}, "cumsum": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.cumsum", "name": "cumsum", "type": "numpy.ma.core._frommethod"}}, "default_fill_value": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.default_fill_value", "name": "default_fill_value", "type": null}}, "diag": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["v", "k"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.diag", "name": "diag", "type": null}}, "diagonal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.diagonal", "name": "diagonal", "type": "numpy.ma.core._frommethod"}}, "diff": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": [null, "n", "axis", "prepend", "append"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.diff", "name": "diff", "type": null}}, "divide": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.divide", "name": "divide", "type": "numpy.ma.core._MaskedBinaryOperation"}}, "dot": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "b", "strict", "out"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.dot", "name": "dot", "type": null}}, "dtype": {".class": "SymbolTableNode", "cross_ref": "numpy.dtype", "kind": "Gdef", "module_hidden": true, "module_public": false}, "empty": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.empty", "name": "empty", "type": "numpy.ma.core._convert2ma"}}, "empty_like": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.empty_like", "name": "empty_like", "type": "numpy.ma.core._convert2ma"}}, "equal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.equal", "name": "equal", "type": "numpy.ma.core._MaskedBinaryOperation"}}, "exp": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.exp", "name": "exp", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "expand_dims": {".class": "SymbolTableNode", "cross_ref": "numpy.lib.shape_base.expand_dims", "kind": "Gdef", "module_public": false}, "fabs": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.fabs", "name": "fabs", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "filled": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "fill_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.filled", "name": "filled", "type": null}}, "fix_invalid": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "mask", "copy", "fill_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.fix_invalid", "name": "fix_invalid", "type": null}}, "flatten_mask": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.flatten_mask", "name": "flatten_mask", "type": null}}, "flatten_structured_array": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.flatten_structured_array", "name": "flatten_structured_array", "type": null}}, "float64": {".class": "SymbolTableNode", "cross_ref": "numpy.float64", "kind": "Gdef", "module_hidden": true, "module_public": false}, "floor": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.floor", "name": "floor", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "floor_divide": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.floor_divide", "name": "floor_divide", "type": "numpy.ma.core._MaskedBinaryOperation"}}, "fmod": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.fmod", "name": "fmod", "type": "numpy.ma.core._MaskedBinaryOperation"}}, "frombuffer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.frombuffer", "name": "frombuffer", "type": "numpy.ma.core._convert2ma"}}, "fromflex": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["fxarray"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.fromflex", "name": "fromflex", "type": null}}, "fromfunction": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.fromfunction", "name": "fromfunction", "type": "numpy.ma.core._convert2ma"}}, "get_data": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.ma.core.get_data", "name": "get_data", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "subok"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_mask": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.ma.core.get_mask", "name": "get_mask", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getdata": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "subok"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.getdata", "name": "getdata", "type": null}}, "getmask": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.getmask", "name": "getmask", "type": null}}, "getmaskarray": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["arr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.get<PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": null}}, "greater": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.greater", "name": "greater", "type": "numpy.ma.core._MaskedBinaryOperation"}}, "greater_equal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.greater_equal", "name": "greater_equal", "type": "numpy.ma.core._MaskedBinaryOperation"}}, "harden_mask": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.harden_mask", "name": "harden_mask", "type": "numpy.ma.core._frommethod"}}, "hypot": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.hypot", "name": "hypot", "type": "numpy.ma.core._MaskedBinaryOperation"}}, "identity": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.identity", "name": "identity", "type": "numpy.ma.core._convert2ma"}}, "ids": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.ids", "name": "ids", "type": "numpy.ma.core._frommethod"}}, "indices": {".class": "SymbolTableNode", "cross_ref": "numpy.core.numeric.indices", "kind": "Gdef", "module_public": false}, "inner": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.inner", "name": "inner", "type": null}}, "innerproduct": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.ma.core.innerproduct", "name": "innerproduct", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "b"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isMA": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.ma.core.isMA", "name": "isMA", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isMaskedArray": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.isMaskedArray", "name": "isMaskedArray", "type": null}}, "is_mask": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["m"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.is_mask", "name": "is_mask", "type": null}}, "is_masked": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.is_masked", "name": "is_masked", "type": null}}, "isarray": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.ma.core.isarray", "name": "isarray", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "left_shift": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.left_shift", "name": "left_shift", "type": null}}, "less": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.less", "name": "less", "type": "numpy.ma.core._MaskedBinaryOperation"}}, "less_equal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.less_equal", "name": "less_equal", "type": "numpy.ma.core._MaskedBinaryOperation"}}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.log", "name": "log", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "log10": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.log10", "name": "log10", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "log2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.log2", "name": "log2", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "logical_and": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.logical_and", "name": "logical_and", "type": "numpy.ma.core._MaskedBinaryOperation"}}, "logical_not": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.logical_not", "name": "logical_not", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "logical_or": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.logical_or", "name": "logical_or", "type": "numpy.ma.core._MaskedBinaryOperation"}}, "logical_xor": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.logical_xor", "name": "logical_xor", "type": "numpy.ma.core._MaskedBinaryOperation"}}, "make_mask": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["m", "copy", "shrink", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.make_mask", "name": "make_mask", "type": null}}, "make_mask_descr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["ndtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.make_mask_descr", "name": "make_mask_descr", "type": null}}, "make_mask_none": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["newshape", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.make_mask_none", "name": "make_mask_none", "type": null}}, "mask_or": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["m1", "m2", "copy", "shrink"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.mask_or", "name": "mask_or", "type": null}}, "mask_rowcols": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.mask_rowcols", "name": "mask_rowcols", "type": null}}, "masked": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.masked", "name": "masked", "type": "numpy.ma.core.MaskedConstant"}}, "masked_array": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy.ma.core.masked_array", "line": 349, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.ma.core.MaskedArray"}}}, "masked_equal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["x", "value", "copy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.masked_equal", "name": "masked_equal", "type": null}}, "masked_greater": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["x", "value", "copy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.masked_greater", "name": "masked_greater", "type": null}}, "masked_greater_equal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["x", "value", "copy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.masked_greater_equal", "name": "masked_greater_equal", "type": null}}, "masked_inside": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["x", "v1", "v2", "copy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.masked_inside", "name": "masked_inside", "type": null}}, "masked_invalid": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "copy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.masked_invalid", "name": "masked_invalid", "type": null}}, "masked_less": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["x", "value", "copy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.masked_less", "name": "masked_less", "type": null}}, "masked_less_equal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["x", "value", "copy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.masked_less_equal", "name": "masked_less_equal", "type": null}}, "masked_not_equal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["x", "value", "copy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.masked_not_equal", "name": "masked_not_equal", "type": null}}, "masked_object": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["x", "value", "copy", "shrink"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.masked_object", "name": "masked_object", "type": null}}, "masked_outside": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["x", "v1", "v2", "copy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.masked_outside", "name": "masked_outside", "type": null}}, "masked_print_option": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.masked_print_option", "name": "masked_print_option", "type": "numpy.ma.core._MaskedPrintOption"}}, "masked_singleton": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.masked_singleton", "name": "masked_singleton", "type": "numpy.ma.core.MaskedConstant"}}, "masked_values": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["x", "value", "rtol", "atol", "copy", "shrink"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.masked_values", "name": "masked_values", "type": null}}, "masked_where": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["condition", "a", "copy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.masked_where", "name": "masked_where", "type": null}}, "max": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["obj", "axis", "out", "fill_value", "keepdims"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.max", "name": "max", "type": null}}, "maximum": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.maximum", "name": "maximum", "type": "numpy.ma.core._extrema_operation"}}, "maximum_fill_value": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.maximum_fill_value", "name": "maximum_fill_value", "type": null}}, "mean": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.mean", "name": "mean", "type": "numpy.ma.core._frommethod"}}, "min": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["obj", "axis", "out", "fill_value", "keepdims"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.min", "name": "min", "type": null}}, "minimum": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.minimum", "name": "minimum", "type": "numpy.ma.core._extrema_operation"}}, "minimum_fill_value": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.minimum_fill_value", "name": "minimum_fill_value", "type": null}}, "mod": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.mod", "name": "mod", "type": "numpy.ma.core._MaskedBinaryOperation"}}, "multiply": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.multiply", "name": "multiply", "type": "numpy.ma.core._MaskedBinaryOperation"}}, "mvoid": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._ShapeType", "id": 1, "name": "_ShapeType", "namespace": "numpy.ma.core.mvoid", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._DType_co", "id": 2, "name": "_DType_co", "namespace": "numpy.ma.core.mvoid", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.ma.core.MaskedArray"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.ma.core.mvoid", "name": "mvoid", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._ShapeType", "id": 1, "name": "_ShapeType", "namespace": "numpy.ma.core.mvoid", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._DType_co", "id": 2, "name": "_DType_co", "namespace": "numpy.ma.core.mvoid", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.ma.core.mvoid", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.ma.core", "mro": ["numpy.ma.core.mvoid", "numpy.ma.core.MaskedArray", "numpy.n<PERSON><PERSON>", "numpy._ArrayOrScalarCommon", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.mvoid.__getitem__", "name": "__getitem__", "type": null}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.mvoid.__iter__", "name": "__iter__", "type": null}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.mvoid.__len__", "name": "__len__", "type": null}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "data", "mask", "dtype", "fill_value", "hardmask", "copy", "subok"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "numpy.ma.core.mvoid.__new__", "name": "__new__", "type": null}}, "__setitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.mvoid.__setitem__", "name": "__setitem__", "type": null}}, "filled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "fill_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.mvoid.filled", "name": "filled", "type": null}}, "tolist": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.mvoid.tolist", "name": "tolist", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core.mvoid.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._ShapeType", "id": 1, "name": "_ShapeType", "namespace": "numpy.ma.core.mvoid", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.ma.core._DType_co", "id": 2, "name": "_DType_co", "namespace": "numpy.ma.core.mvoid", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.ma.core.mvoid"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ShapeType", "_DType_co"], "typeddict_type": null}}, "ndarray": {".class": "SymbolTableNode", "cross_ref": "numpy.n<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ndim": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.ndim", "name": "ndim", "type": null}}, "negative": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.negative", "name": "negative", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "nomask": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.nomask", "name": "nomask", "type": "numpy.bool_"}}, "nonzero": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.nonzero", "name": "nonzero", "type": "numpy.ma.core._frommethod"}}, "not_equal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.not_equal", "name": "not_equal", "type": "numpy.ma.core._MaskedBinaryOperation"}}, "ones": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.ones", "name": "ones", "type": "numpy.ma.core._convert2ma"}}, "ones_like": {".class": "SymbolTableNode", "cross_ref": "numpy.core.numeric.ones_like", "kind": "Gdef", "module_public": false}, "outer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.outer", "name": "outer", "type": null}}, "outerproduct": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.ma.core.outerproduct", "name": "outerproduct", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "b"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "power": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "third"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.power", "name": "power", "type": null}}, "prod": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.prod", "name": "prod", "type": "numpy.ma.core._frommethod"}}, "product": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.product", "name": "product", "type": "numpy.ma.core._frommethod"}}, "ptp": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["obj", "axis", "out", "fill_value", "keepdims"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.ptp", "name": "ptp", "type": null}}, "put": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["a", "indices", "values", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.put", "name": "put", "type": null}}, "putmask": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["a", "mask", "values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.putmask", "name": "putmask", "type": null}}, "ravel": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.ravel", "name": "ravel", "type": "numpy.ma.core._frommethod"}}, "remainder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.remainder", "name": "remainder", "type": "numpy.ma.core._MaskedBinaryOperation"}}, "repeat": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.repeat", "name": "repeat", "type": "numpy.ma.core._frommethod"}}, "reshape": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["a", "new_shape", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.reshape", "name": "reshape", "type": null}}, "resize": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["x", "new_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.resize", "name": "resize", "type": null}}, "right_shift": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.right_shift", "name": "right_shift", "type": null}}, "round": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["a", "decimals", "out"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.round", "name": "round", "type": null}}, "set_fill_value": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "fill_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.set_fill_value", "name": "set_fill_value", "type": null}}, "shape": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.shape", "name": "shape", "type": null}}, "sin": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.sin", "name": "sin", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "sinh": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.sinh", "name": "sinh", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "size": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["obj", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.size", "name": "size", "type": null}}, "soften_mask": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.soften_mask", "name": "soften_mask", "type": "numpy.ma.core._frommethod"}}, "sometrue": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.sometrue", "name": "sometrue", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sort": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["a", "axis", "kind", "order", "endwith", "fill_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.sort", "name": "sort", "type": null}}, "sqrt": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.sqrt", "name": "sqrt", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "squeeze": {".class": "SymbolTableNode", "cross_ref": "numpy.core.fromnumeric.squeeze", "kind": "Gdef", "module_public": false}, "std": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.std", "name": "std", "type": "numpy.ma.core._frommethod"}}, "subtract": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.subtract", "name": "subtract", "type": "numpy.ma.core._MaskedBinaryOperation"}}, "sum": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.sum", "name": "sum", "type": "numpy.ma.core._frommethod"}}, "swapaxes": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.swapaxes", "name": "swapaxes", "type": "numpy.ma.core._frommethod"}}, "take": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["a", "indices", "axis", "out", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.take", "name": "take", "type": null}}, "tan": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.tan", "name": "tan", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "tanh": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.tanh", "name": "tanh", "type": "numpy.ma.core._MaskedUnaryOperation"}}, "trace": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.trace", "name": "trace", "type": "numpy.ma.core._frommethod"}}, "transpose": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "axes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.transpose", "name": "transpose", "type": null}}, "true_divide": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.true_divide", "name": "true_divide", "type": "numpy.ma.core._MaskedBinaryOperation"}}, "var": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.var", "name": "var", "type": "numpy.ma.core._frommethod"}}, "where": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["condition", "x", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.ma.core.where", "name": "where", "type": null}}, "zeros": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.ma.core.zeros", "name": "zeros", "type": "numpy.ma.core._convert2ma"}}, "zeros_like": {".class": "SymbolTableNode", "cross_ref": "numpy.core.numeric.zeros_like", "kind": "Gdef", "module_public": false}}, "path": "c:\\AnacondaPath\\Lib\\site-packages\\numpy\\ma\\core.pyi"}