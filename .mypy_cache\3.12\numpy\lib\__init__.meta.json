{"data_mtime": 1749245426, "dep_lines": [13, 13, 13, 13, 20, 24, 28, 39, 43, 87, 93, 108, 125, 141, 156, 181, 199, 214, 220, 236, 4, 11, 1, 2, 6, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30], "dependencies": ["numpy.lib.format", "numpy.lib.mixins", "numpy.lib.scimath", "numpy.lib.stride_tricks", "numpy.lib._version", "numpy.lib.arraypad", "numpy.lib.arraysetops", "numpy.lib.arrayterator", "numpy.lib.function_base", "numpy.lib.histograms", "numpy.lib.index_tricks", "numpy.lib.nanfunctions", "numpy.lib.npyio", "numpy.lib.polynomial", "numpy.lib.shape_base", "numpy.lib.twodim_base", "numpy.lib.type_check", "numpy.lib.ufunclike", "numpy.lib.utils", "numpy.core.multiarray", "numpy._pytesttester", "numpy.version", "math", "typing", "numpy", "builtins", "_frozen_importlib", "abc", "types"], "hash": "f23821129e9faaff9676d5607b5284d095ac9734", "id": "numpy.lib", "ignore_all": true, "interface_hash": "f18bb71f2da1017f7cc1fba85c898bd65c3e7cbf", "mtime": 1707226024, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\AnacondaPath\\Lib\\site-packages\\numpy\\lib\\__init__.pyi", "plugin_data": null, "size": 5596, "suppressed": [], "version_id": "1.15.0"}