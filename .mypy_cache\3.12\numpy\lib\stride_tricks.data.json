{".class": "MypyFile", "_fullname": "numpy.lib.stride_tricks", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DummyArray": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.lib.stride_tricks.DummyA<PERSON>y", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.lib.stride_tricks.DummyA<PERSON>y", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.lib.stride_tricks", "mro": ["numpy.lib.stride_tricks.DummyA<PERSON>y", "builtins.object"], "names": {".class": "SymbolTable", "__array_interface__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib.stride_tricks.DummyArray.__array_interface__", "name": "__array_interface__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "interface", "base"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib.stride_tricks.DummyArray.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "interface", "base"], "arg_types": ["numpy.lib.stride_tricks.DummyA<PERSON>y", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON>mmyA<PERSON>y", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.lib.stride_tricks.DummyArray.base", "name": "base", "type": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks.DummyArray.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.lib.stride_tricks.DummyA<PERSON>y", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NDArray": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.NDArray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SupportsIndex": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsIndex", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_SCT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "name": "_SCT", "upper_bound": "numpy.generic", "values": [], "variance": 0}}, "_Shape": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._shape._Shape", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ShapeLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._shape._ShapeLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.stride_tricks.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.stride_tricks.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.stride_tricks.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.stride_tricks.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.stride_tricks.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.stride_tricks.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.stride_tricks.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "as_strided": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib.stride_tricks.as_strided", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["x", "shape", "strides", "subok", "writeable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.stride_tricks.as_strided", "name": "as_strided", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["x", "shape", "strides", "subok", "writeable"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.as_strided#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_strided", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.as_strided#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.as_strided#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.stride_tricks.as_strided", "name": "as_strided", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["x", "shape", "strides", "subok", "writeable"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.as_strided#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_strided", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.as_strided#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.as_strided#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["x", "shape", "strides", "subok", "writeable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.stride_tricks.as_strided", "name": "as_strided", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["x", "shape", "strides", "subok", "writeable"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_strided", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.stride_tricks.as_strided", "name": "as_strided", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["x", "shape", "strides", "subok", "writeable"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_strided", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["x", "shape", "strides", "subok", "writeable"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.as_strided#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_strided", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.as_strided#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.as_strided#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["x", "shape", "strides", "subok", "writeable"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_strided", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "broadcast_arrays": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 5], "arg_names": ["args", "subok"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib.stride_tricks.broadcast_arrays", "name": "broadcast_arrays", "type": {".class": "CallableType", "arg_kinds": [2, 5], "arg_names": ["args", "subok"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "broadcast_arrays", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "broadcast_shapes": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib.stride_tricks.broadcast_shapes", "name": "broadcast_shapes", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._ShapeLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "broadcast_shapes", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._Shape"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "broadcast_to": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib.stride_tricks.broadcast_to", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["array", "shape", "subok"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.stride_tricks.broadcast_to", "name": "broadcast_to", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["array", "shape", "subok"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.broadcast_to#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "broadcast_to", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.broadcast_to#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.broadcast_to#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.stride_tricks.broadcast_to", "name": "broadcast_to", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["array", "shape", "subok"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.broadcast_to#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "broadcast_to", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.broadcast_to#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.broadcast_to#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["array", "shape", "subok"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.stride_tricks.broadcast_to", "name": "broadcast_to", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["array", "shape", "subok"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "broadcast_to", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.stride_tricks.broadcast_to", "name": "broadcast_to", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["array", "shape", "subok"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "broadcast_to", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["array", "shape", "subok"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.broadcast_to#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "broadcast_to", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.broadcast_to#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.broadcast_to#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["array", "shape", "subok"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "broadcast_to", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "generic": {".class": "SymbolTableNode", "cross_ref": "numpy.generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sliding_window_view": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib.stride_tricks.sliding_window_view", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["x", "window_shape", "axis", "subok", "writeable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.stride_tricks.sliding_window_view", "name": "sliding_window_view", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["x", "window_shape", "axis", "subok", "writeable"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.sliding_window_view#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sliding_window_view", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.sliding_window_view#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.sliding_window_view#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.stride_tricks.sliding_window_view", "name": "sliding_window_view", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["x", "window_shape", "axis", "subok", "writeable"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.sliding_window_view#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sliding_window_view", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.sliding_window_view#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.sliding_window_view#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["x", "window_shape", "axis", "subok", "writeable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.stride_tricks.sliding_window_view", "name": "sliding_window_view", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["x", "window_shape", "axis", "subok", "writeable"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sliding_window_view", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.stride_tricks.sliding_window_view", "name": "sliding_window_view", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["x", "window_shape", "axis", "subok", "writeable"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sliding_window_view", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["x", "window_shape", "axis", "subok", "writeable"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.sliding_window_view#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sliding_window_view", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.sliding_window_view#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.stride_tricks._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.stride_tricks.sliding_window_view#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["x", "window_shape", "axis", "subok", "writeable"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sliding_window_view", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "path": "c:\\AnacondaPath\\Lib\\site-packages\\numpy\\lib\\stride_tricks.pyi"}