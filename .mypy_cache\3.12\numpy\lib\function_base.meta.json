{"data_mtime": 1749245426, "dep_lines": [51, 55, 60, 2, 33, 1, 3, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["numpy.core.function_base", "numpy.core.multiarray", "numpy.core.umath", "collections.abc", "numpy._typing", "sys", "typing", "numpy", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nested_sequence", "types"], "hash": "0a6be8fcb4422528470a9ce0b6df196ac9e995c5", "id": "numpy.lib.function_base", "ignore_all": true, "interface_hash": "3de0946f43c7b4a778e70cc5239086ace4d6c7b6", "mtime": 1707226024, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\AnacondaPath\\Lib\\site-packages\\numpy\\lib\\function_base.pyi", "plugin_data": null, "size": 16585, "suppressed": [], "version_id": "1.15.0"}