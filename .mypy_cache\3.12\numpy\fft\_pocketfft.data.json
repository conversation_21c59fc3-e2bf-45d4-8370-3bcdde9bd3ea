{".class": "MypyFile", "_fullname": "numpy.fft._pocketfft", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "L": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NDArray": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.NDArray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeNumber_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeNumber_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_NormKind": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy.fft._pocketfft._NormKind", "line": 7, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "backward"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ortho"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "forward"}], "uses_pep604_syntax": false}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.fft._pocketfft.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.fft._pocketfft.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.fft._pocketfft.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.fft._pocketfft.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.fft._pocketfft.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.fft._pocketfft.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.fft._pocketfft.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "complex128": {".class": "SymbolTableNode", "cross_ref": "numpy.complex128", "kind": "Gdef", "module_hidden": true, "module_public": false}, "fft": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "n", "axis", "norm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.fft._pocketfft.fft", "name": "fft", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "n", "axis", "norm"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.fft._pocketfft._NormKind"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fft", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex128"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fft2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "s", "axes", "norm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.fft._pocketfft.fft2", "name": "fft2", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "s", "axes", "norm"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.fft._pocketfft._NormKind"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fft2", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex128"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fftn": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "s", "axes", "norm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.fft._pocketfft.fftn", "name": "fftn", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "s", "axes", "norm"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.fft._pocketfft._NormKind"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fftn", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex128"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "float64": {".class": "SymbolTableNode", "cross_ref": "numpy.float64", "kind": "Gdef", "module_hidden": true, "module_public": false}, "hfft": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "n", "axis", "norm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.fft._pocketfft.hfft", "name": "hfft", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "n", "axis", "norm"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.fft._pocketfft._NormKind"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hfft", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ifft": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "n", "axis", "norm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.fft._pocketfft.ifft", "name": "ifft", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "n", "axis", "norm"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.fft._pocketfft._NormKind"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ifft", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex128"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ifft2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "s", "axes", "norm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.fft._pocketfft.ifft2", "name": "ifft2", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "s", "axes", "norm"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.fft._pocketfft._NormKind"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ifft2", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex128"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ifftn": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "s", "axes", "norm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.fft._pocketfft.ifftn", "name": "ifftn", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "s", "axes", "norm"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.fft._pocketfft._NormKind"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ifftn", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex128"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ihfft": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "n", "axis", "norm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.fft._pocketfft.ihfft", "name": "ihfft", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "n", "axis", "norm"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.fft._pocketfft._NormKind"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ihfft", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex128"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "irfft": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "n", "axis", "norm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.fft._pocketfft.irfft", "name": "irfft", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "n", "axis", "norm"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.fft._pocketfft._NormKind"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "irfft", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "irfft2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "s", "axes", "norm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.fft._pocketfft.irfft2", "name": "irfft2", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "s", "axes", "norm"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.fft._pocketfft._NormKind"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "irfft2", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "irfftn": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "s", "axes", "norm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.fft._pocketfft.irfftn", "name": "irfftn", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "s", "axes", "norm"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.fft._pocketfft._NormKind"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "irfftn", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rfft": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "n", "axis", "norm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.fft._pocketfft.rfft", "name": "rfft", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "n", "axis", "norm"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.fft._pocketfft._NormKind"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rfft", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex128"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rfft2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "s", "axes", "norm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.fft._pocketfft.rfft2", "name": "rfft2", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "s", "axes", "norm"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.fft._pocketfft._NormKind"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rfft2", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex128"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rfftn": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "s", "axes", "norm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.fft._pocketfft.rfftn", "name": "rfftn", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "s", "axes", "norm"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.fft._pocketfft._NormKind"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rfftn", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex128"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\AnacondaPath\\Lib\\site-packages\\numpy\\fft\\_pocketfft.pyi"}