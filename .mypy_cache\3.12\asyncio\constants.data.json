{".class": "MypyFile", "_fullname": "asyncio.constants", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ACCEPT_RETRY_DELAY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "asyncio.constants.ACCEPT_RETRY_DELAY", "name": "ACCEPT_RETRY_DELAY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "DEBUG_STACK_DEPTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 10, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "asyncio.constants.DEBUG_STACK_DEPTH", "name": "DEBUG_STACK_DEPTH", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 10}, "type_ref": "builtins.int"}}}, "FLOW_CONTROL_HIGH_WATER_SSL_READ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 256, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "asyncio.constants.FLOW_CONTROL_HIGH_WATER_SSL_READ", "name": "FLOW_CONTROL_HIGH_WATER_SSL_READ", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 256}, "type_ref": "builtins.int"}}}, "FLOW_CONTROL_HIGH_WATER_SSL_WRITE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 512, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "asyncio.constants.FLOW_CONTROL_HIGH_WATER_SSL_WRITE", "name": "FLOW_CONTROL_HIGH_WATER_SSL_WRITE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 512}, "type_ref": "builtins.int"}}}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LOG_THRESHOLD_FOR_CONNLOST_WRITES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 5, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "asyncio.constants.LOG_THRESHOLD_FOR_CONNLOST_WRITES", "name": "LOG_THRESHOLD_FOR_CONNLOST_WRITES", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, "type_ref": "builtins.int"}}}, "SENDFILE_FALLBACK_READBUFFER_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 262144, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "asyncio.constants.SENDFILE_FALLBACK_READBUFFER_SIZE", "name": "SENDFILE_FALLBACK_READBUFFER_SIZE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 262144}, "type_ref": "builtins.int"}}}, "SSL_HANDSHAKE_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.constants.SSL_HANDSHAKE_TIMEOUT", "name": "SSL_HANDSHAKE_TIMEOUT", "type": "builtins.float"}}, "SSL_SHUTDOWN_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.constants.SSL_SHUTDOWN_TIMEOUT", "name": "SSL_SHUTDOWN_TIMEOUT", "type": "builtins.float"}}, "THREAD_JOIN_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 300, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "asyncio.constants.THREAD_JOIN_TIMEOUT", "name": "THREAD_JOIN_TIMEOUT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 300}, "type_ref": "builtins.int"}}}, "_SendfileMode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asyncio.constants._SendfileMode", "name": "_SendfileMode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "asyncio.constants._SendfileMode", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "asyncio.constants", "mro": ["asyncio.constants._SendfileMode", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "FALLBACK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "asyncio.constants._SendfileMode.FALLBACK", "name": "FALLBACK", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "TRY_NATIVE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "asyncio.constants._SendfileMode.TRY_NATIVE", "name": "TRY_NATIVE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "UNSUPPORTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "asyncio.constants._SendfileMode.UNSUPPORTED", "name": "UNSUPPORTED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.constants._SendfileMode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.constants._SendfileMode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.constants.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.constants.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.constants.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.constants.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.constants.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.constants.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\asyncio\\constants.pyi"}