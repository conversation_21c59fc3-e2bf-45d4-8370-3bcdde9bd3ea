{"cells": [{"cell_type": "code", "execution_count": null, "id": "45aa02df", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np  # noqa: F401\n"]}, {"cell_type": "code", "execution_count": 64, "id": "f8248ff1", "metadata": {}, "outputs": [], "source": ["jord = pd.read_csv('Jordstykker_RS.csv')\n", "stam = pd.read_csv('Stamdata_RS.csv')\n", "ejendomme = pd.read_excel('250604_arealoversigt_Resights_2026_RawData.xlsx', sheet_name='<PERSON><PERSON><PERSON><PERSON>')"]}, {"cell_type": "code", "execution_count": 65, "id": "8cb630c9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Jordstykker: (493, 26)\n", "Stamdata: (139, 101)\n", "Ejendomme: (493, 26)\n"]}], "source": ["print('<PERSON><PERSON><PERSON><PERSON>:', jord.shape)\n", "print('Stamdata:', stam.shape)  \n", "print('Ejendomme:', ejendomme.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "7c67bccd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Jordstykker_RS.csv info:\n", "\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 493 entries, 0 to 492\n", "Data columns (total 26 columns):\n", " #   Column                                 Non-Null Count  Dtype  \n", "---  ------                                 --------------  -----  \n", " 0   BFE-nummer                             493 non-null    int64  \n", " 1   JordstykkeID                           493 non-null    int64  \n", " 2   Matrikelnr                             493 non-null    object \n", " 3   Ejerlavsnavn                           493 non-null    object \n", " 4   Matrikeldato (virkningsdato)           493 non-null    object \n", " 5   <PERSON>r fælleslod                           493 non-null    object \n", " 6   Registreret areal                      493 non-null    object \n", " 7   Vejareal                               493 non-null    object \n", " 8   Fredskovsareal                         493 non-null    object \n", " 9   Klitfredningsreal                      493 non-null    object \n", " 10  Strandbeskyttelsesareal                493 non-null    object \n", " 11  Brugsretsareal                         493 non-null    object \n", " 12  Areal beregningsmetode                 493 non-null    object \n", " 13  Areal betegnelse                       493 non-null    object \n", " 14  Areal type                             493 non-null    object \n", " 15  Fredskovsareal omfang                  493 non-null    object \n", " 16  Jordrente omfang                       493 non-null    object \n", " 17  Klitfredning omfang                    493 non-null    object \n", " 18  Majoratskov omfang                     493 non-null    object \n", " 19  Strandbeskyttelse omfang               493 non-null    object \n", " 20  Påbud vedrørende spildevandsafledning  493 non-null    object \n", " 21  Frist vedrørende spildevandsafledning  493 non-null    object \n", " 22  Longitude                              493 non-null    float64\n", " 23  Latitude                               493 non-null    float64\n", " 24  Kommunekode                            493 non-null    int64  \n", " 25  Ejendomsnummer                         493 non-null    int64  \n", "dtypes: float64(2), int64(4), object(20)\n", "memory usage: 100.3+ KB\n", "None\n", "   BFE-nummer  JordstykkeID Matrikelnr               Ejerlavsnavn  \\\n", "0   100675838       2034170         2g   Over Hornbæk By, Hornbæk   \n", "1   100675838       2032926        6go  <PERSON>, Hornbæk   \n", "2     3094239       1696596        10b            <PERSON>sø By, Fuglse   \n", "3     3094238       1696599        10a            Alsø By, Fuglse   \n", "4     3094238       1696588        11a            Alsø By, Fuglse   \n", "\n", "  Matrikeldato (virkningsdato) Er fælleslod Registreret areal Vejareal  \\\n", "0          2023-10-25 12:40:02          <PERSON><PERSON>           36,169        -    \n", "1          2023-10-25 12:40:02          <PERSON>ej           17,059        -    \n", "2          1994-05-18 22:00:00          Nej            1,690        -    \n", "3          2025-03-14 13:59:50          <PERSON><PERSON>          563,714    1,950    \n", "4          2012-04-30 11:17:50          <PERSON><PERSON>           27,002        -    \n", "\n", "  Fredskovsareal Klitfredningsreal Strandbeskyttelsesareal Brugsretsareal  \\\n", "0             -                 -                       -            <PERSON><PERSON>    \n", "1             -                 -                       -            <PERSON><PERSON>    \n", "2         1,690                 -                       -            <PERSON><PERSON>    \n", "3       432,600                 -                       -            <PERSON><PERSON>    \n", "4        27,002                 -                       -            <PERSON><PERSON>    \n", "\n", "            Areal beregningsmetode Areal betegnelse Areal type  \\\n", "0  <PERSON>l beregnet efter kortet - k                -          -   \n", "1  Areal beregnet efter kortet - k                -          -   \n", "2  Areal beregnet efter kortet - k                -          -   \n", "3  Areal beregnet efter kortet - k                -          -   \n", "4  Areal beregnet efter kortet - k                -          -   \n", "\n", "  Fredskovsareal omfang Jordrente omfang Klitfredning omfang  \\\n", "0                     -                -                   -   \n", "1                     -                -                   -   \n", "2                   Hel                -                   -   \n", "3                Delvis                -                   -   \n", "4                   Hel                -                   -   \n", "\n", "  Majoratskov omfang Strandbeskyttelse omfang  \\\n", "0                  -                        -   \n", "1                  -                        -   \n", "2                  -                        -   \n", "3                  -                        -   \n", "4                  -                        -   \n", "\n", "  Påbud vedrørende spildevandsafledning Frist vedrørende spildevandsafledning  \\\n", "0                                     -                                     -   \n", "1                                     -                                     -   \n", "2                                     -                                     -   \n", "3                                     -                                     -   \n", "4                                     -                                     -   \n", "\n", "   Longitude   Latitude  Kommunekode  Ejendomsnummer  \n", "0   9.973892  56.449836          730            8462  \n", "1   9.989764  56.461012          730            8462  \n", "2  11.557957  54.718318          360              54  \n", "3  11.565228  54.720222          376           20531  \n", "4  11.562260  54.720080          376           20531  \n", "         BFE-nummer  JordstykkeID   Longitude    Latitude  Kommunekode  \\\n", "count  4.930000e+02  4.930000e+02  493.000000  493.000000   493.000000   \n", "mean   3.001822e+07  1.070050e+07   10.019383   56.251951   655.535497   \n", "std    3.962289e+07  2.876951e+07    0.913514    0.866128   185.592080   \n", "min    1.401799e+06  3.708700e+04    8.163539   54.718290   230.000000   \n", "25%    8.522442e+06  1.473790e+05    9.259897   55.572959   480.000000   \n", "50%    8.966667e+06  2.033347e+06   10.175748   56.451658   730.000000   \n", "75%    1.022405e+07  2.451447e+06   10.271322   56.941915   820.000000   \n", "max    1.006758e+08  1.008659e+08   15.086495   57.693632   851.000000   \n", "\n", "       Ejendomsnummer  \n", "count      493.000000  \n", "mean    147406.993915  \n", "std     224152.630466  \n", "min         54.000000  \n", "25%      10993.000000  \n", "50%      25433.000000  \n", "75%     113753.000000  \n", "max     634495.000000  \n"]}], "source": ["pd.set_option('display.max_columns', None)\n", "print('<PERSON>rdstykker_RS.csv info:\\n')\n", "print(jord.info())\n", "print(jord.head())\n", "print(jord.describe())"]}, {"cell_type": "code", "execution_count": null, "id": "9144a9b6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Stamdata_RS.csv info:\n", "\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 139 entries, 0 to 138\n", "Columns: 101 entries, BFE-nummer to Ejerlavsnavn\n", "dtypes: int64(12), object(89)\n", "memory usage: 109.8+ KB\n", "None\n", "   BF<PERSON>-num<PERSON>  \\\n", "0   100675838            Aage Beks Vej 25, 8920 Randers NV   \n", "1     3094239                <PERSON><PERSON><PERSON> 27, <PERSON><PERSON><PERSON>, 4960 <PERSON><PERSON>   \n", "2     3094238                <PERSON>sø<PERSON><PERSON> 31, <PERSON><PERSON><PERSON>, 4960 <PERSON><PERSON>   \n", "3     8316982  <PERSON><PERSON><PERSON><PERSON> 100, <PERSON><PERSON>, 9690 <PERSON><PERSON><PERSON><PERSON>   \n", "4     3094471           Bagskovvej 2A, <PERSON><PERSON><PERSON><PERSON>, 4960 <PERSON><PERSON>   \n", "\n", "                P<PERSON>ær ejer Anvendelse Energimærke  Beboelsesenheder  \\\n", "0  AAGE V. JENSEN NATURFOND          -       A2015                 0   \n", "1  AAGE V. JENSEN NATURFOND          -           -                 0   \n", "2  AAGE V. JENSEN NATURFOND          -           -                 0   \n", "3  AAGE V. JENSEN NATURFOND          -           -                 0   \n", "4  AAGE V. JENSEN NATURFOND          -           -                 0   \n", "\n", "   Erhvervsenheder Opførelsesår Grundareal Beboelsesareal Erhvervsareal  \\\n", "0                0            -    53,228              -             -    \n", "1                0            -     1,690              -             -    \n", "2                0            -   605,161              -             -    \n", "3                0            -   125,325              -             -    \n", "4                0            -     2,216              -             -    \n", "\n", "  Offentlig eje<PERSON>værdi (Ny) Offentlig grundværdi (Ny) Seneste handelspris  \\\n", "0                       - kr.                     - kr.         399,210 kr.    \n", "1                       - kr.                     - kr.       2,328,545 kr.    \n", "2                       - kr.                     - kr.      18,500,000 kr.    \n", "3                       - kr.                     - kr.         700,000 kr.    \n", "4                       - kr.                     - kr.          44,320 kr.    \n", "\n", "  Pris pr. m2 (BR18-§455 etageareal) Seneste handelsdato                 Type  \\\n", "0                             - kr.           2023-12-21  <PERSON><PERSON> fast ejendom   \n", "1                             - kr.           2020-02-01  Samlet fast ejendom   \n", "2                       163,717 kr.           2019-09-26  Samlet fast ejendom   \n", "3                            70 kr.           2020-04-30  Samlet fast ejendom   \n", "4                             - kr.           2022-12-19  <PERSON><PERSON> fast ejendom   \n", "\n", "             Undertype                                               Link  \\\n", "0  Samlet fast ejendom  https://app.resights.dk/aage-beks-vej-25-8920-...   \n", "1  Samlet fast ejendom  https://app.resights.dk/alsovej-27-also-4960-h...   \n", "2  Samlet fast ejendom  https://app.resights.dk/alsovej-31-also-4960-h...   \n", "3  Samlet fast ejendom  https://app.resights.dk/alvejen-100-thorup-hol...   \n", "4  Samlet fast ejendom  https://app.resights.dk/bagskovvej-2a-fuglse-4...   \n", "\n", "   Antal ejere Øvrige ejere Primær ejer reklamebeskyttet Primær ejer alder  \\\n", "0            1            -                           <PERSON>a                 -   \n", "1            1            -                           Ja                 -   \n", "2            1            -                           <PERSON>a                 -   \n", "3            1            -                           <PERSON>a                 -   \n", "4            1            -                           <PERSON><PERSON>                 -   \n", "\n", "  Ejerskabstype VurderingsejendomsID Offentlig ejendomsværdi (Historisk)  \\\n", "0      Forening                    -                              - kr.    \n", "1      Forening               759625                        190,000 kr.    \n", "2      Forening               841029                     11,100,000 kr.    \n", "3      Forening              2141184                      1,100,000 kr.    \n", "4      Forening               760990                      4,700,000 kr.    \n", "\n", "  <PERSON>ent<PERSON><PERSON> (Historisk) Vurderingsår (Historisk) Vurderingsår (Ny)  \\\n", "0                           - kr.                         -                 -   \n", "1                     150,800 kr.                      2020                 -   \n", "2                   2,111,200 kr.                      2020                 -   \n", "3                     364,300 kr.                      2020                 -   \n", "4                     953,200 kr.                      2020                 -   \n", "\n", "  Offentlig ejendomsværdi (Foreløbig) Offentlig grundværdi (Foreløbig)  \\\n", "0                              - kr.                    6,286,000 kr.    \n", "1                              - kr.                        5,000 kr.    \n", "2                        696,000 kr.                    1,265,000 kr.    \n", "3                              - kr.                      367,000 kr.    \n", "4                        459,000 kr.                    1,157,000 kr.    \n", "\n", "  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Foreløbig) Juridisk kategori <PERSON>ld (Historisk)  \\\n", "0                     2023                 -                 - kr.    \n", "1                     2023                 -             2,667 kr.    \n", "2                     2023                 -                 - kr.    \n", "3                     2023                 -                 - kr.    \n", "4                     2023                 -                 - kr.    \n", "\n", "  Dækningsafgift (Historisk) Ejendomsværdiskat (Historisk)  \\\n", "0                     - kr.                         - kr.    \n", "1                     - kr.                         - kr.    \n", "2                     - kr.                         - kr.    \n", "3                     - kr.                         - kr.    \n", "4                     - kr.                         - kr.    \n", "\n", "  Totale skat (Historisk) Grundskyld (Ny) Ejendomsværdiskat (Ny)  \\\n", "0              6,935 kr.           - kr.                  - kr.    \n", "1              2,667 kr.           - kr.                  - kr.    \n", "2                  - kr.           - kr.                  - kr.    \n", "3              2,623 kr.           - kr.                  - kr.    \n", "4              6,863 kr.           - kr.                  - kr.    \n", "\n", "  Totale skat (Ny) Grundskyld (Foreløbig) Ejendomsværdiskat (Foreløbig)  \\\n", "0           - kr.             69,900 kr.                         - kr.    \n", "1           - kr.                 70 kr.                         - kr.    \n", "2           - kr.                  - kr.                         - kr.    \n", "3           - kr.              3,876 kr.                         - kr.    \n", "4           - kr.                  - kr.                         - kr.    \n", "\n", "  Totale skat (Foreløbig) Realiseret bebyggelsesprocent  \\\n", "0                  - kr.                             0%   \n", "1                 70 kr.                             0%   \n", "2                  - kr.                             0%   \n", "3                  - kr.                             0%   \n", "4                  - kr.                             0%   \n", "\n", "  Etageareal (BR18-§455) Bygningsareal Antal værelser Enhedsareal - Beboelse  \\\n", "0                     -             -              -                      -    \n", "1                     -             -              -                      -    \n", "2                     -             -              -                      -    \n", "3                     -             -              -                      -    \n", "4                     -             -              -                      -    \n", "\n", "  Enhedsareal - Erhverv Areal af udnyttet del af tagetage Kælderareal  \\\n", "0                    -                                 -           -    \n", "1                    -                                 -           -    \n", "2                    -                                 -           -    \n", "3                    -                                 -           -    \n", "4                    -                                 -           -    \n", "\n", "  Pris pr. m2 (samlet enhedsareal) Pris pr. m2 (enhedsareal)  \\\n", "0                           - kr.                     - kr.    \n", "1                           - kr.                     - kr.    \n", "2                     183,168 kr.                     - kr.    \n", "3                          72 kr.                    81 kr.    \n", "4                           - kr.                     - kr.    \n", "\n", "  Pris pr. m2 (grund) Seneste handlet andel          Handelsmetode  \\\n", "0              8 kr.                   100%  Almindelig fri handel   \n", "1              1 kr.                   100%  Almindelig fri handel   \n", "2             13 kr.                   100%  Almindelig fri handel   \n", "3              0 kr.                   100%  Almindelig fri handel   \n", "4             20 kr.                   100%  Almindelig fri handel   \n", "\n", "   Seneste handel (antal ejendomme) Anvendelseskode Omtilbygningsår  \\\n", "0                                 1               -               -   \n", "1                                 2               -               -   \n", "2                                 2               -               -   \n", "3                                 8               -               -   \n", "4                                 1               -               -   \n", "\n", "  Ejerlejlighedsnummer Ejerlejlighedsdato (virkningsdato)  \\\n", "0                    -                                  -   \n", "1                    -                                  -   \n", "2                    -                                  -   \n", "3                    -                                  -   \n", "4                    -                                  -   \n", "\n", "  Tinglyst areal (Ejerlejligheder) Fordelingstal (Tæller)  \\\n", "0                               -                      -    \n", "1                               -                      -    \n", "2                               -                      -    \n", "3                               -                      -    \n", "4                               -                      -    \n", "\n", "  Fordelingstal (Nævner)      Virksomhedsstatus Virksomhedstype  \\\n", "0                     -   Erhvervsdrivende fond          NORMAL   \n", "1                     -   Erhvervsdrivende fond          NORMAL   \n", "2                     -   Erhvervsdrivende fond          NORMAL   \n", "3                     -   Erhvervsdrivende fond          NORMAL   \n", "4                     -   Erhvervsdrivende fond          NORMAL   \n", "\n", "  Virksomhed kontaktperson navn Virksomhed kontaktperson rolle  \\\n", "0            Hanne Haack Larsen                       Direktør   \n", "1            Hanne Haack Larsen                       Direktør   \n", "2            <PERSON><PERSON><PERSON>ør   \n", "3            <PERSON><PERSON> Haack <PERSON>rektør   \n", "4            <PERSON><PERSON> Ha<PERSON>rek<PERSON>ør   \n", "\n", "   Virksomhed telefon Virksomhed email Ejendomsadministrator  \\\n", "0            33132145    <EMAIL>                     -   \n", "1            33132145    <EMAIL>                     -   \n", "2            33132145    <EMAIL>                     -   \n", "3            33132145    <EMAIL>                     -   \n", "4            33132145    <EMAIL>                     -   \n", "\n", "  Ejendomsadministrator CVR-nummer    Longitude     Latitude        Vejnavn  \\\n", "0                                -   9.99315105  56.46401941  Aage Beks Vej   \n", "1                                -  11.55803985  54.71841369        Alsøvej   \n", "2                                -  11.56230377  54.71985645        Alsøvej   \n", "3                                -   9.10068719  57.06435546        Alvejen   \n", "4                                -  11.54482767  54.71796034     Bagskovvej   \n", "\n", "  Husnr. Etage Dør Postnr           By                Postlinje 1  \\\n", "0     25     -   -   8920   Randers NV           Aage Beks Vej 25   \n", "1     27     -   -   4960       Holeby           Alsøvej 27, <PERSON><PERSON><PERSON>   \n", "2     31     -   -   4960       Holeby           Alsøvej 31, <PERSON><PERSON><PERSON>   \n", "3    100     -   -   9690  <PERSON><PERSON><PERSON><PERSON> 100, <PERSON><PERSON>   \n", "4     2A     -   -   4960       Holeby      Bagskovvej 2A, <PERSON><PERSON><PERSON><PERSON>   \n", "\n", "        Postlinje 2  Primær ejer CVR-nummer  \\\n", "0   8920 Randers NV                30205596   \n", "1       4960 Holeby                30205596   \n", "2       4960 Holeby                30205596   \n", "3  9690 <PERSON><PERSON><PERSON><PERSON>                30205596   \n", "4       4960 Holeby                30205596   \n", "\n", "                    <PERSON><PERSON><PERSON>r ejers adresse Primær ejer Vejnavn  \\\n", "0  Kampmannsgade 1, 6., 1604 København V       Kampmannsgade   \n", "1  Kampmannsgade 1, 6., 1604 København V       Kampmannsgade   \n", "2  Kampmannsgade 1, 6., 1604 København V       Kampmannsgade   \n", "3  Kampmannsgade 1, 6., 1604 København V       Kampmannsgade   \n", "4  Kampmannsgade 1, 6., 1604 København V       Kampmannsgade   \n", "\n", "   Primær ejer husnr.  Primær ejer etage Primær ejer dør  Primær ejer postnr.  \\\n", "0                   1                  6               -                 1604   \n", "1                   1                  6               -                 1604   \n", "2                   1                  6               -                 1604   \n", "3                   1                  6               -                 1604   \n", "4                   1                  6               -                 1604   \n", "\n", "  <PERSON><PERSON><PERSON><PERSON> ejer by <PERSON><PERSON><PERSON><PERSON> ejer land Primær ejer - Postlinje 1  \\\n", "0    København V          Danmark       Kampmannsgade 1, 6.   \n", "1    København V          Danmark       Kampmannsgade 1, 6.   \n", "2    København V          Danmark       Kampmannsgade 1, 6.   \n", "3    København V          Danmark       Kampmannsgade 1, 6.   \n", "4    København V          Danmark       Kampmannsgade 1, 6.   \n", "\n", "  Primær ejer - Postlinje 2           Kommunenavn Vejkode  \\\n", "0                         -       Randers Kommune       1   \n", "1                         -       <PERSON><PERSON> Kommune      26   \n", "2                         -  Guldborgsund Kommune      26   \n", "3                         -    Jammerbugt Kommune      55   \n", "4                         -       <PERSON><PERSON>      70   \n", "\n", "                       AdgangsadresseID                             AdresseID  \\\n", "0  0a3f5094-e2e6-32b8-e044-0003ba298018  0a3f50c1-57cb-32b8-e044-0003ba298018   \n", "1  0a3f5085-3ae2-32b8-e044-0003ba298018  0a3f50af-94b8-32b8-e044-0003ba298018   \n", "2  99da8452-1849-48d0-9317-aaa789aba00d  1ad648e2-a2af-4f02-9e59-3bcda9d65303   \n", "3  77c8e586-bed9-497a-befa-76d9706a0c8e  4c464b66-e4af-4447-b022-8615e6fb04c5   \n", "4  1debe65f-d9e6-47ec-8355-0f612dbd6185  c5cc03e9-743a-4311-8b7a-fd8dde2698ef   \n", "\n", "   Kommunekode  Ejendomsnummer Matrikelnr              Ejerlavsnavn  \n", "0          730            8462         2g  Over Hornbæk By, Hornbæk  \n", "1          360              54        10b           <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>  \n", "2          376           20531        10a           <PERSON>s<PERSON>, Fu<PERSON><PERSON><PERSON>  \n", "3          849           73102        18n     <PERSON><PERSON>, <PERSON><PERSON>  \n", "4          360            1475        58y         <PERSON>, <PERSON><PERSON><PERSON><PERSON>  \n", "         BFE-nummer  Beboelsesenheder  Erhvervsenheder  Antal ejere  \\\n", "count  1.390000e+02        139.000000       139.000000        139.0   \n", "mean   1.705634e+07          0.151079         0.302158          1.0   \n", "std    3.121863e+07          0.588706         0.937578          0.0   \n", "min    7.436650e+05          0.000000         0.000000          1.0   \n", "25%    3.332674e+06          0.000000         0.000000          1.0   \n", "50%    5.085979e+06          0.000000         0.000000          1.0   \n", "75%    8.966670e+06          0.000000         0.000000          1.0   \n", "max    1.006758e+08          4.000000         8.000000          1.0   \n", "\n", "       Seneste handel (antal ejendomme)  Virksomhed telefon  \\\n", "count                        139.000000               139.0   \n", "mean                           1.280576          33132145.0   \n", "std                            1.329862                 0.0   \n", "min                            1.000000          33132145.0   \n", "25%                            1.000000          33132145.0   \n", "50%                            1.000000          33132145.0   \n", "75%                            1.000000          33132145.0   \n", "max                           10.000000          33132145.0   \n", "\n", "       Primær ejer CVR-nummer  Primær ejer husnr.  Primær ejer etage  \\\n", "count            1.390000e+02               139.0              139.0   \n", "mean             3.027925e+07                 1.0                6.0   \n", "std              2.809231e+05                 0.0                0.0   \n", "min              3.020560e+07                 1.0                6.0   \n", "25%              3.020560e+07                 1.0                6.0   \n", "50%              3.020560e+07                 1.0                6.0   \n", "75%              3.020560e+07                 1.0                6.0   \n", "max              3.134307e+07                 1.0                6.0   \n", "\n", "       <PERSON><PERSON><PERSON><PERSON> ejer postnr.  Kommunekode  Ejendomsnummer  \n", "count                139.0   139.000000      139.000000  \n", "mean                1604.0   646.395683   120410.115108  \n", "std                    0.0   182.871643   174559.001944  \n", "min                 1604.0   230.000000       54.000000  \n", "25%                 1604.0   486.000000    17047.000000  \n", "50%                 1604.0   657.000000    95605.000000  \n", "75%                 1604.0   813.000000   109776.500000  \n", "max                 1604.0   851.000000   634495.000000  \n"]}], "source": ["print('\\nStamdata_RS.csv info:\\n')\n", "print(stam.info())\n", "print(stam.head())\n", "print(stam.describe())"]}, {"cell_type": "markdown", "id": "0723b938", "metadata": {}, "source": ["## Filtrering of data from AVJNF\n", "\n", "Jeg kan se, at der er andre ejendomme fra andre datterselvskaber i datasættet. Disses skal filtreres fra, så vi kun har ejendomme fra AVJNF.\n"]}, {"cell_type": "code", "execution_count": null, "id": "b7c95a6c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "<PERSON><PERSON><PERSON><PERSON> ejer distribution:\n", "[<PERSON><PERSON><PERSON><PERSON> ejer\n", "AAGE V. JENSEN NATURFOND    130\n", "Lille Vildmose Naturfond      9\n", "Name: count, dtype: int64, ['AAGE V. JENSEN NATURFOND', 'Lille Vildmose Naturfond']]\n", "\n", "AAGE V. JENSEN NATURFOND info:\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 139 entries, 0 to 138\n", "Columns: 101 entries, BFE-nummer to Ejerlavsnavn\n", "dtypes: int64(12), object(89)\n", "memory usage: 109.8+ KB\n", "None\n", "\n", "\n", "   BF<PERSON>-num<PERSON>  \\\n", "0   100675838            Aage Beks Vej 25, 8920 Randers NV   \n", "1     3094239                <PERSON><PERSON><PERSON> 27, <PERSON><PERSON><PERSON>, 4960 <PERSON><PERSON>   \n", "2     3094238                <PERSON>sø<PERSON><PERSON> 31, <PERSON><PERSON><PERSON>, 4960 <PERSON><PERSON>   \n", "3     8316982  <PERSON><PERSON><PERSON><PERSON> 100, <PERSON><PERSON>, 9690 <PERSON><PERSON><PERSON><PERSON>   \n", "4     3094471           Bagskovvej 2A, <PERSON><PERSON><PERSON><PERSON>, 4960 <PERSON><PERSON>   \n", "\n", "                P<PERSON>ær ejer Anvendelse Energimærke  Beboelsesenheder  \\\n", "0  AAGE V. JENSEN NATURFOND          -       A2015                 0   \n", "1  AAGE V. JENSEN NATURFOND          -           -                 0   \n", "2  AAGE V. JENSEN NATURFOND          -           -                 0   \n", "3  AAGE V. JENSEN NATURFOND          -           -                 0   \n", "4  AAGE V. JENSEN NATURFOND          -           -                 0   \n", "\n", "   Erhvervsenheder Opførelsesår Grundareal Beboelsesareal Erhvervsareal  \\\n", "0                0            -    53,228              -             -    \n", "1                0            -     1,690              -             -    \n", "2                0            -   605,161              -             -    \n", "3                0            -   125,325              -             -    \n", "4                0            -     2,216              -             -    \n", "\n", "  Offentlig eje<PERSON>værdi (Ny) Offentlig grundværdi (Ny) Seneste handelspris  \\\n", "0                       - kr.                     - kr.         399,210 kr.    \n", "1                       - kr.                     - kr.       2,328,545 kr.    \n", "2                       - kr.                     - kr.      18,500,000 kr.    \n", "3                       - kr.                     - kr.         700,000 kr.    \n", "4                       - kr.                     - kr.          44,320 kr.    \n", "\n", "  Pris pr. m2 (BR18-§455 etageareal) Seneste handelsdato                 Type  \\\n", "0                             - kr.           2023-12-21  <PERSON><PERSON> fast ejendom   \n", "1                             - kr.           2020-02-01  Samlet fast ejendom   \n", "2                       163,717 kr.           2019-09-26  Samlet fast ejendom   \n", "3                            70 kr.           2020-04-30  Samlet fast ejendom   \n", "4                             - kr.           2022-12-19  <PERSON><PERSON> fast ejendom   \n", "\n", "             Undertype                                               Link  \\\n", "0  Samlet fast ejendom  https://app.resights.dk/aage-beks-vej-25-8920-...   \n", "1  Samlet fast ejendom  https://app.resights.dk/alsovej-27-also-4960-h...   \n", "2  Samlet fast ejendom  https://app.resights.dk/alsovej-31-also-4960-h...   \n", "3  Samlet fast ejendom  https://app.resights.dk/alvejen-100-thorup-hol...   \n", "4  Samlet fast ejendom  https://app.resights.dk/bagskovvej-2a-fuglse-4...   \n", "\n", "   Antal ejere Øvrige ejere Primær ejer reklamebeskyttet Primær ejer alder  \\\n", "0            1            -                           <PERSON>a                 -   \n", "1            1            -                           Ja                 -   \n", "2            1            -                           <PERSON>a                 -   \n", "3            1            -                           <PERSON>a                 -   \n", "4            1            -                           <PERSON><PERSON>                 -   \n", "\n", "  Ejerskabstype VurderingsejendomsID Offentlig ejendomsværdi (Historisk)  \\\n", "0      Forening                    -                              - kr.    \n", "1      Forening               759625                        190,000 kr.    \n", "2      Forening               841029                     11,100,000 kr.    \n", "3      Forening              2141184                      1,100,000 kr.    \n", "4      Forening               760990                      4,700,000 kr.    \n", "\n", "  <PERSON>ent<PERSON><PERSON> (Historisk) Vurderingsår (Historisk) Vurderingsår (Ny)  \\\n", "0                           - kr.                         -                 -   \n", "1                     150,800 kr.                      2020                 -   \n", "2                   2,111,200 kr.                      2020                 -   \n", "3                     364,300 kr.                      2020                 -   \n", "4                     953,200 kr.                      2020                 -   \n", "\n", "  Offentlig ejendomsværdi (Foreløbig) Offentlig grundværdi (Foreløbig)  \\\n", "0                              - kr.                    6,286,000 kr.    \n", "1                              - kr.                        5,000 kr.    \n", "2                        696,000 kr.                    1,265,000 kr.    \n", "3                              - kr.                      367,000 kr.    \n", "4                        459,000 kr.                    1,157,000 kr.    \n", "\n", "  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Foreløbig) Juridisk kategori <PERSON>ld (Historisk)  \\\n", "0                     2023                 -                 - kr.    \n", "1                     2023                 -             2,667 kr.    \n", "2                     2023                 -                 - kr.    \n", "3                     2023                 -                 - kr.    \n", "4                     2023                 -                 - kr.    \n", "\n", "  Dækningsafgift (Historisk) Ejendomsværdiskat (Historisk)  \\\n", "0                     - kr.                         - kr.    \n", "1                     - kr.                         - kr.    \n", "2                     - kr.                         - kr.    \n", "3                     - kr.                         - kr.    \n", "4                     - kr.                         - kr.    \n", "\n", "  Totale skat (Historisk) Grundskyld (Ny) Ejendomsværdiskat (Ny)  \\\n", "0              6,935 kr.           - kr.                  - kr.    \n", "1              2,667 kr.           - kr.                  - kr.    \n", "2                  - kr.           - kr.                  - kr.    \n", "3              2,623 kr.           - kr.                  - kr.    \n", "4              6,863 kr.           - kr.                  - kr.    \n", "\n", "  Totale skat (Ny) Grundskyld (Foreløbig) Ejendomsværdiskat (Foreløbig)  \\\n", "0           - kr.             69,900 kr.                         - kr.    \n", "1           - kr.                 70 kr.                         - kr.    \n", "2           - kr.                  - kr.                         - kr.    \n", "3           - kr.              3,876 kr.                         - kr.    \n", "4           - kr.                  - kr.                         - kr.    \n", "\n", "  Totale skat (Foreløbig) Realiseret bebyggelsesprocent  \\\n", "0                  - kr.                             0%   \n", "1                 70 kr.                             0%   \n", "2                  - kr.                             0%   \n", "3                  - kr.                             0%   \n", "4                  - kr.                             0%   \n", "\n", "  Etageareal (BR18-§455) Bygningsareal Antal værelser Enhedsareal - Beboelse  \\\n", "0                     -             -              -                      -    \n", "1                     -             -              -                      -    \n", "2                     -             -              -                      -    \n", "3                     -             -              -                      -    \n", "4                     -             -              -                      -    \n", "\n", "  Enhedsareal - Erhverv Areal af udnyttet del af tagetage Kælderareal  \\\n", "0                    -                                 -           -    \n", "1                    -                                 -           -    \n", "2                    -                                 -           -    \n", "3                    -                                 -           -    \n", "4                    -                                 -           -    \n", "\n", "  Pris pr. m2 (samlet enhedsareal) Pris pr. m2 (enhedsareal)  \\\n", "0                           - kr.                     - kr.    \n", "1                           - kr.                     - kr.    \n", "2                     183,168 kr.                     - kr.    \n", "3                          72 kr.                    81 kr.    \n", "4                           - kr.                     - kr.    \n", "\n", "  Pris pr. m2 (grund) Seneste handlet andel          Handelsmetode  \\\n", "0              8 kr.                   100%  Almindelig fri handel   \n", "1              1 kr.                   100%  Almindelig fri handel   \n", "2             13 kr.                   100%  Almindelig fri handel   \n", "3              0 kr.                   100%  Almindelig fri handel   \n", "4             20 kr.                   100%  Almindelig fri handel   \n", "\n", "   Seneste handel (antal ejendomme) Anvendelseskode Omtilbygningsår  \\\n", "0                                 1               -               -   \n", "1                                 2               -               -   \n", "2                                 2               -               -   \n", "3                                 8               -               -   \n", "4                                 1               -               -   \n", "\n", "  Ejerlejlighedsnummer Ejerlejlighedsdato (virkningsdato)  \\\n", "0                    -                                  -   \n", "1                    -                                  -   \n", "2                    -                                  -   \n", "3                    -                                  -   \n", "4                    -                                  -   \n", "\n", "  Tinglyst areal (Ejerlejligheder) Fordelingstal (Tæller)  \\\n", "0                               -                      -    \n", "1                               -                      -    \n", "2                               -                      -    \n", "3                               -                      -    \n", "4                               -                      -    \n", "\n", "  Fordelingstal (Nævner)      Virksomhedsstatus Virksomhedstype  \\\n", "0                     -   Erhvervsdrivende fond          NORMAL   \n", "1                     -   Erhvervsdrivende fond          NORMAL   \n", "2                     -   Erhvervsdrivende fond          NORMAL   \n", "3                     -   Erhvervsdrivende fond          NORMAL   \n", "4                     -   Erhvervsdrivende fond          NORMAL   \n", "\n", "  Virksomhed kontaktperson navn Virksomhed kontaktperson rolle  \\\n", "0            Hanne Haack Larsen                       Direktør   \n", "1            Hanne Haack Larsen                       Direktør   \n", "2            <PERSON><PERSON><PERSON>ør   \n", "3            <PERSON><PERSON> Haack <PERSON>rektør   \n", "4            <PERSON><PERSON> Ha<PERSON>rek<PERSON>ør   \n", "\n", "   Virksomhed telefon Virksomhed email Ejendomsadministrator  \\\n", "0            33132145    <EMAIL>                     -   \n", "1            33132145    <EMAIL>                     -   \n", "2            33132145    <EMAIL>                     -   \n", "3            33132145    <EMAIL>                     -   \n", "4            33132145    <EMAIL>                     -   \n", "\n", "  Ejendomsadministrator CVR-nummer    Longitude     Latitude        Vejnavn  \\\n", "0                                -   9.99315105  56.46401941  Aage Beks Vej   \n", "1                                -  11.55803985  54.71841369        Alsøvej   \n", "2                                -  11.56230377  54.71985645        Alsøvej   \n", "3                                -   9.10068719  57.06435546        Alvejen   \n", "4                                -  11.54482767  54.71796034     Bagskovvej   \n", "\n", "  Husnr. Etage Dør Postnr           By                Postlinje 1  \\\n", "0     25     -   -   8920   Randers NV           Aage Beks Vej 25   \n", "1     27     -   -   4960       Holeby           Alsøvej 27, <PERSON><PERSON><PERSON>   \n", "2     31     -   -   4960       Holeby           Alsøvej 31, <PERSON><PERSON><PERSON>   \n", "3    100     -   -   9690  <PERSON><PERSON><PERSON><PERSON> 100, <PERSON><PERSON>   \n", "4     2A     -   -   4960       Holeby      Bagskovvej 2A, <PERSON><PERSON><PERSON><PERSON>   \n", "\n", "        Postlinje 2  Primær ejer CVR-nummer  \\\n", "0   8920 Randers NV                30205596   \n", "1       4960 Holeby                30205596   \n", "2       4960 Holeby                30205596   \n", "3  9690 <PERSON><PERSON><PERSON><PERSON>                30205596   \n", "4       4960 Holeby                30205596   \n", "\n", "                    <PERSON><PERSON><PERSON>r ejers adresse Primær ejer Vejnavn  \\\n", "0  Kampmannsgade 1, 6., 1604 København V       Kampmannsgade   \n", "1  Kampmannsgade 1, 6., 1604 København V       Kampmannsgade   \n", "2  Kampmannsgade 1, 6., 1604 København V       Kampmannsgade   \n", "3  Kampmannsgade 1, 6., 1604 København V       Kampmannsgade   \n", "4  Kampmannsgade 1, 6., 1604 København V       Kampmannsgade   \n", "\n", "   Primær ejer husnr.  Primær ejer etage Primær ejer dør  Primær ejer postnr.  \\\n", "0                   1                  6               -                 1604   \n", "1                   1                  6               -                 1604   \n", "2                   1                  6               -                 1604   \n", "3                   1                  6               -                 1604   \n", "4                   1                  6               -                 1604   \n", "\n", "  <PERSON><PERSON><PERSON><PERSON> ejer by <PERSON><PERSON><PERSON><PERSON> ejer land Primær ejer - Postlinje 1  \\\n", "0    København V          Danmark       Kampmannsgade 1, 6.   \n", "1    København V          Danmark       Kampmannsgade 1, 6.   \n", "2    København V          Danmark       Kampmannsgade 1, 6.   \n", "3    København V          Danmark       Kampmannsgade 1, 6.   \n", "4    København V          Danmark       Kampmannsgade 1, 6.   \n", "\n", "  Primær ejer - Postlinje 2           Kommunenavn Vejkode  \\\n", "0                         -       Randers Kommune       1   \n", "1                         -       <PERSON><PERSON> Kommune      26   \n", "2                         -  Guldborgsund Kommune      26   \n", "3                         -    Jammerbugt Kommune      55   \n", "4                         -       <PERSON><PERSON>      70   \n", "\n", "                       AdgangsadresseID                             AdresseID  \\\n", "0  0a3f5094-e2e6-32b8-e044-0003ba298018  0a3f50c1-57cb-32b8-e044-0003ba298018   \n", "1  0a3f5085-3ae2-32b8-e044-0003ba298018  0a3f50af-94b8-32b8-e044-0003ba298018   \n", "2  99da8452-1849-48d0-9317-aaa789aba00d  1ad648e2-a2af-4f02-9e59-3bcda9d65303   \n", "3  77c8e586-bed9-497a-befa-76d9706a0c8e  4c464b66-e4af-4447-b022-8615e6fb04c5   \n", "4  1debe65f-d9e6-47ec-8355-0f612dbd6185  c5cc03e9-743a-4311-8b7a-fd8dde2698ef   \n", "\n", "   Kommunekode  Ejendomsnummer Matrikelnr              Ejerlavsnavn  \n", "0          730            8462         2g  Over Hornbæk By, Hornbæk  \n", "1          360              54        10b           <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>  \n", "2          376           20531        10a           <PERSON>s<PERSON>, Fu<PERSON><PERSON><PERSON>  \n", "3          849           73102        18n     <PERSON><PERSON>, <PERSON><PERSON>  \n", "4          360            1475        58y         <PERSON>, <PERSON><PERSON><PERSON><PERSON>  \n"]}], "source": ["stam_ejer_dist = [stam[\"<PERSON>rim<PERSON><PERSON> ejer\"].value_counts(),\n", "                 stam[\"<PERSON><PERSON><PERSON><PERSON> ejer\"].unique().tolist()\n", "                ]\n", "print(f'\\nPrimær ejer distribution:\\n{stam_ejer_dist}')\n", "\n", "stam_avjnf = stam[\n", "    (stam['<PERSON><PERSON><PERSON><PERSON> ejer'] == 'AAGE V. JENSEN NATURFOND') |\n", "    (stam['<PERSON><PERSON><PERSON><PERSON> ejer'] == 'Lille Vildmose Naturfond')\n", "]\n", "print('\\nAAGE V. JENSEN NATURFOND info:')\n", "print(stam_avjnf.info())\n", "print('\\n')\n", "print(stam_avjnf.head())"]}, {"cell_type": "markdown", "id": "fd630284", "metadata": {}, "source": ["## Filtering af data i Jordstykker_RS fra filtreret Stamdata_RS"]}, {"cell_type": "code", "execution_count": null, "id": "e7996ff3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Unique JordstykkeID values in jord: 493\n", "\n", "Potential foreign keys in stam_avjnf matching JordstykkeID:\n", "Total columns checked: 101\n", "Columns with matches: 0\n", "No matching columns found!\n", "\n", "First few JordstykkeID values:\n", "['2449800', '54424', '147341', '2451440', '678117', '147371', '958623', '54534', '119944', '147347']\n", "\n", "Sample of stam_avjnf column names:\n", "['BF<PERSON>-nummer', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON> ejer', '<PERSON><PERSON><PERSON><PERSON>', 'Energimærke', 'Beboelsesenheder', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>hed<PERSON>', 'Opf<PERSON>relsesår', 'G<PERSON>dar<PERSON>', 'Beboelsesareal']\n"]}], "source": ["# Get unique values from JordstykkeID in jord\n", "jordstykke_ids = set(jord[\"JordstykkeID\"].dropna().astype(str))\n", "print(f\"Unique JordstykkeID values in jord: {len(jordstykke_ids)}\")\n", "\n", "# Check each column in stam_avjnf (filtered data) for matches with JordstykkeID\n", "potential_foreign_keys = {}\n", "\n", "for col in stam_avjnf.columns:\n", "    try:\n", "        stam_values = set(stam_avjnf[col].dropna().astype(str))\n", "        overlap = jordstykke_ids & stam_values\n", "\n", "        if len(overlap) > 0:\n", "            ratio = len(overlap) / len(jordstykke_ids)\n", "            potential_foreign_keys[col] = {\n", "                \"overlap\": len(overlap),\n", "                \"ratio\": ratio,\n", "                \"stam_unique_count\": len(stam_values),\n", "            }\n", "    except (<PERSON><PERSON><PERSON><PERSON>, ValueError, AttributeError):\n", "        continue\n", "\n", "# Display results sorted by overlap ratio\n", "print(\"\\nPotential foreign keys in stam_avjnf matching JordstykkeID:\")\n", "print(f\"Total columns checked: {len(stam_avjnf.columns)}\")\n", "print(f\"Columns with matches: {len(potential_foreign_keys)}\")\n", "\n", "if potential_foreign_keys:\n", "    for col, stats in sorted(\n", "        potential_foreign_keys.items(), key=lambda x: x[1][\"ratio\"], reverse=True\n", "    ):\n", "        print(f\"\\nColumn '{col}':\")\n", "        print(f\"  - Matching values: {stats['overlap']}\")\n", "        print(f\"  - Match ratio: {stats['ratio']:.2%}\")\n", "        print(f\"  - Unique values in stam column: {stats['stam_unique_count']}\")\n", "else:\n", "    print(\"No matching columns found!\")\n", "    print(\"\\nFirst few JordstykkeID values:\")\n", "    print(list(jordstykke_ids)[:10])\n", "    print(\"\\nSample of stam_avjnf column names:\")\n", "    print(stam_avjnf.columns.tolist()[:10])"]}, {"cell_type": "code", "execution_count": null, "id": "15cf7fcf", "metadata": {}, "outputs": [], "source": ["for col in stam_avjnf.columns:\n", "    try:\n", "        stam_values = set(stam_avjnf[col].dropna().astype(str))\n", "        overlap = jordstykke_ids & stam_values\n", "        \n", "        if len(overlap) > 0:\n", "            ratio = len(overlap) / len(jordstykke_ids)\n", "            potential_foreign_keys[col] = {\n", "                \"overlap\": len(overlap),\n", "                \"ratio\": ratio,\n", "                \"stam_unique_count\": len(stam_values),\n", "            }\n", "    except (<PERSON><PERSON><PERSON><PERSON>, ValueError, AttributeError):\n", "        # Skip columns that can't be converted to string or processed\n", "        continue"]}, {"cell_type": "code", "execution_count": null, "id": "c0b25181", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Filtered Jordstykker for AAGE V. JENSEN NATURFOND:\n", "Original jord shape: (493, 26)\n", "Filtered jord_avjnf shape: (493, 26)\n", "Number of unique BFE-nummer in stam_avjnf: 139\n", "Number of unique BFE-nummer in jord_avjnf: 138\n", "\n", "First few rows of filtered jordstykker:\n", "   BFE-nummer  JordstykkeID Matrikelnr               Ejerlavsnavn  \\\n", "0   100675838       2034170         2g   Over Hornbæk By, Hornbæk   \n", "1   100675838       2032926        6go  <PERSON>, Hornbæk   \n", "2     3094239       1696596        10b            <PERSON>sø By, Fuglse   \n", "3     3094238       1696599        10a            Alsø By, Fuglse   \n", "4     3094238       1696588        11a            Alsø By, Fuglse   \n", "\n", "  Matrikeldato (virkningsdato) Er fælleslod Registreret areal Vejareal  \\\n", "0          2023-10-25 12:40:02          <PERSON><PERSON>           36,169        -    \n", "1          2023-10-25 12:40:02          <PERSON>ej           17,059        -    \n", "2          1994-05-18 22:00:00          Nej            1,690        -    \n", "3          2025-03-14 13:59:50          <PERSON><PERSON>          563,714    1,950    \n", "4          2012-04-30 11:17:50          <PERSON><PERSON>           27,002        -    \n", "\n", "  Fredskovsareal Klitfredningsreal Strandbeskyttelsesareal Brugsretsareal  \\\n", "0             -                 -                       -            <PERSON><PERSON>    \n", "1             -                 -                       -            <PERSON><PERSON>    \n", "2         1,690                 -                       -            <PERSON><PERSON>    \n", "3       432,600                 -                       -            <PERSON><PERSON>    \n", "4        27,002                 -                       -            <PERSON><PERSON>    \n", "\n", "            Areal beregningsmetode Areal betegnelse Areal type  \\\n", "0  <PERSON>l beregnet efter kortet - k                -          -   \n", "1  Areal beregnet efter kortet - k                -          -   \n", "2  Areal beregnet efter kortet - k                -          -   \n", "3  Areal beregnet efter kortet - k                -          -   \n", "4  Areal beregnet efter kortet - k                -          -   \n", "\n", "  Fredskovsareal omfang Jordrente omfang Klitfredning omfang  \\\n", "0                     -                -                   -   \n", "1                     -                -                   -   \n", "2                   Hel                -                   -   \n", "3                Delvis                -                   -   \n", "4                   Hel                -                   -   \n", "\n", "  Majoratskov omfang Strandbeskyttelse omfang  \\\n", "0                  -                        -   \n", "1                  -                        -   \n", "2                  -                        -   \n", "3                  -                        -   \n", "4                  -                        -   \n", "\n", "  Påbud vedrørende spildevandsafledning Frist vedrørende spildevandsafledning  \\\n", "0                                     -                                     -   \n", "1                                     -                                     -   \n", "2                                     -                                     -   \n", "3                                     -                                     -   \n", "4                                     -                                     -   \n", "\n", "   Longitude   Latitude  Kommunekode  Ejendomsnummer  \n", "0   9.973892  56.449836          730            8462  \n", "1   9.989764  56.461012          730            8462  \n", "2  11.557957  54.718318          360              54  \n", "3  11.565228  54.720222          376           20531  \n", "4  11.562260  54.720080          376           20531  \n", "\n", "Unique BFE-nummer values in filtered data:\n", "[np.int64(1401799), np.int64(1417480), np.int64(1490192), np.int64(2378591), np.int64(2378611), np.int64(2671205), np.int64(2671229), np.int64(3038349), np.int64(3056511), np.int64(3094238), np.int64(3094239), np.int64(3094471), np.int64(3103074), np.int64(3103075), np.int64(3103077), np.int64(3103078), np.int64(3153203), np.int64(3154204), np.int64(3154209), np.int64(3154229), np.int64(3154230), np.int64(3259914), np.int64(3319308), np.int64(3319385), np.int64(3319419), np.int64(3321007), np.int64(3321008), np.int64(3321009), np.int64(3321011), np.int64(3321012), np.int64(3322489), np.int64(3322535), np.int64(3322590), np.int64(3332672), np.int64(3332675), np.int64(3343126), np.int64(3393461), np.int64(3396386), np.int64(3396457), np.int64(3396490), np.int64(3406352), np.int64(3442995), np.int64(4102072), np.int64(4102873), np.int64(4102874), np.int64(4454691), np.int64(4458841), np.int64(4458842), np.int64(4458844), np.int64(4458845), np.int64(4458854), np.int64(4458857), np.int64(4458860), np.int64(4458862), np.int64(4464763), np.int64(4464765), np.int64(4464769), np.int64(4464791), np.int64(4464793), np.int64(4464794), np.int64(4464835), np.int64(4464836), np.int64(4464837), np.int64(4464838), np.int64(4464839), np.int64(4464840), np.int64(4464841), np.int64(4464842), np.int64(5085979), np.int64(5085983), np.int64(5085986), np.int64(5090004), np.int64(5090021), np.int64(5090090), np.int64(5090326), np.int64(5091701), np.int64(5297349), np.int64(5445050), np.int64(5531602), np.int64(5531640), np.int64(5531648), np.int64(7189174), np.int64(7396964), np.int64(7793262), np.int64(7802336), np.int64(7982192), np.int64(8076328), np.int64(8255217), np.int64(8316982), np.int64(8398824), np.int64(8504512), np.int64(8504514), np.int64(8504613), np.int64(8522442), np.int64(8557179), np.int64(8589446), np.int64(8589464), np.int64(8589491), np.int64(8589553), np.int64(8773384), np.int64(8846812), np.int64(8897447), np.int64(8966667), np.int64(8966672), np.int64(9038606), np.int64(9038607), np.int64(9038615), np.int64(9167136), np.int64(9359599), np.int64(9407818), np.int64(9428326), np.int64(9428347), np.int64(9682695), np.int64(9801012), np.int64(9852287), np.int64(9897121), np.int64(10021063), np.int64(10160825), np.int64(10209040), np.int64(10223924), np.int64(10224052), np.int64(100008043), np.int64(100013097), np.int64(100031320), np.int64(100034604), np.int64(100034605), np.int64(100042561), np.int64(100049064), np.int64(100055829), np.int64(100056873), np.int64(100062363), np.int64(100078994), np.int64(100086061), np.int64(100166561), np.int64(100227900), np.int64(100240077), np.int64(100442891), np.int64(100675838)]\n"]}], "source": ["# Filter jord to only include BFE-nummer values that exist in stam_avjnf\n", "jord_avjnf = jord[jord[\"BFE-nummer\"].isin(stam_avjnf[\"BFE-nummer\"])]\n", "\n", "print(\"\\nFiltered Jordstykker for AAGE V. JENSEN NATURFOND:\")\n", "print(f\"Original jord shape: {jord.shape}\")\n", "print(f\"Filtered jord_avjnf shape: {jord_avjnf.shape}\")\n", "print(\n", "    f'Number of unique BFE-nummer in stam_avjnf: {stam_avjnf[\"BFE-nummer\"].nunique()}'\n", ")\n", "print(\n", "    f'Number of unique BFE-nummer in jord_avjnf: {jord_avjnf[\"BFE-nummer\"].nunique()}'\n", ")\n", "\n", "print(\"\\nFirst few rows of filtered jordstykker:\")\n", "print(jord_avjnf.head())\n", "\n", "# Verify the filtering worked correctly\n", "print(\"\\nUnique BFE-nummer values in filtered data:\")\n", "print(sorted(jord_avjnf[\"BFE-nummer\"].unique()))"]}, {"cell_type": "code", "execution_count": null, "id": "312a24ab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "BFE-nummer values in stam_avjnf but NOT in jord:\n", "Count: 1\n", "Values: [743665]\n", "\n", "Properties without land parcels:\n", "    BFE-nummer                        Adresse                      Type                                   Anvendelse\n", "47      743665  Fjordvang 24, 8940 Randers SV  Bygning på fremmed grund  Anden bygning til undervisning og forskning\n", "\n", "BFE-nummer in jord_avjnf but NOT in stam_avjnf: 0\n"]}], "source": ["# Find BFE-nummer values that exist in stam_avjnf but not in jord_avjnf\n", "missing_in_jord = set(stam_avjnf[\"BFE-nummer\"]) - set(jord_avjnf[\"BFE-nummer\"])\n", "\n", "print(\"\\nBFE-nummer values in stam_avjnf but NOT in jord:\")\n", "print(f\"Count: {len(missing_in_jord)}\")\n", "print(f\"Values: {sorted(missing_in_jord)}\")\n", "\n", "# Show the properties that don't have land parcels\n", "if missing_in_jord:\n", "    print(\"\\nProperties without land parcels:\")\n", "    missing_properties = stam_avjnf[stam_avjnf[\"BFE-nummer\"].isin(missing_in_jord)]\n", "    print(\n", "        missing_properties[[\"BFE-nummer\", \"<PERSON><PERSON><PERSON>\", \"Type\", \"An<PERSON>del<PERSON>\"]].to_string()\n", "    )\n", "\n", "# Also check the reverse - any BFE in jord but not in stam_avjnf (should be 0)\n", "missing_in_stam = set(jord_avjnf[\"BFE-nummer\"]) - set(stam_avjnf[\"BFE-nummer\"])\n", "print(f\"\\nBFE-nummer in jord_avjnf but NOT in stam_avjnf: {len(missing_in_stam)}\")"]}], "metadata": {"kernelspec": {"display_name": "jupyter_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 5}