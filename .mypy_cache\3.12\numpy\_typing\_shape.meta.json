{"data_mtime": 1749245416, "dep_lines": [1, 2, 1, 1, 1], "dep_prios": [5, 5, 5, 30, 30], "dependencies": ["collections.abc", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "3b9115083260f1a7a07d41052d4e6681a66416bf", "id": "numpy._typing._shape", "ignore_all": true, "interface_hash": "c7613f843fb754f786b36e80def0d377160ab62b", "mtime": 1707226024, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\AnacondaPath\\Lib\\site-packages\\numpy\\_typing\\_shape.py", "plugin_data": null, "size": 211, "suppressed": [], "version_id": "1.15.0"}