{"data_mtime": 1749245426, "dep_lines": [14, 16, 1, 2, 12, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 30, 30, 30], "dependencies": ["numpy._typing._shape", "numpy._typing._char_codes", "collections.abc", "typing", "numpy", "builtins", "_frozen_importlib", "abc", "types"], "hash": "f11605253784d7226699b0029ce3da407b45e41e", "id": "numpy._typing._dtype_like", "ignore_all": true, "interface_hash": "5e7367b23c078e820945edf905a6d4670a1e0bf7", "mtime": 1707226024, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\AnacondaPath\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py", "plugin_data": null, "size": 5661, "suppressed": [], "version_id": "1.15.0"}