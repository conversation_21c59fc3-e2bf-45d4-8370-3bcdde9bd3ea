{".class": "MypyFile", "_fullname": "numpy.core.numerictypes", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DTypeLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like.DTypeLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "L": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ScalarType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.numerictypes.ScalarType", "name": "ScalarType", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": "builtins.int"}, {".class": "TypeType", "item": "builtins.float"}, {".class": "TypeType", "item": "builtins.complex"}, {".class": "TypeType", "item": "builtins.bool"}, {".class": "TypeType", "item": "builtins.bytes"}, {".class": "TypeType", "item": "builtins.str"}, {".class": "TypeType", "item": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}}, {".class": "TypeType", "item": "numpy.bool_"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitSingle"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitSingle"}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitLongDouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitLongDouble"}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitHalf"}], "extra_attrs": null, "type_ref": "numpy.floating"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitSingle"}], "extra_attrs": null, "type_ref": "numpy.floating"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "extra_attrs": null, "type_ref": "numpy.floating"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitLongDouble"}], "extra_attrs": null, "type_ref": "numpy.floating"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitByte"}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitShort"}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitIntC"}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitLongLong"}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}}, {".class": "TypeType", "item": "numpy.timedelta64"}, {".class": "TypeType", "item": "numpy.datetime64"}, {".class": "TypeType", "item": "numpy.object_"}, {".class": "TypeType", "item": "numpy.bytes_"}, {".class": "TypeType", "item": "numpy.str_"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitByte"}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitShort"}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitIntC"}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitLongLong"}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}}, {".class": "TypeType", "item": "numpy.void"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CastFunc": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.core.numerictypes._CastFunc", "name": "_CastFunc", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy.core.numerictypes._CastFunc", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy.core.numerictypes", "mro": ["numpy.core.numerictypes._CastFunc", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "k"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.numerictypes._CastFunc.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "k"], "arg_types": ["numpy.core.numerictypes._CastFunc", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _CastFun<PERSON>", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._CastFunc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.core.numerictypes._CastFunc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_DTypeLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_SCT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "name": "_SCT", "upper_bound": "numpy.generic", "values": [], "variance": 0}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_TypeCodes": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.core.numerictypes._TypeCodes", "name": "_TypeCodes", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.core.numerictypes._TypeCodes", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.core.numerictypes", "mro": ["numpy.core.numerictypes._TypeCodes", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["Character", {".class": "LiteralType", "fallback": "builtins.str", "value": "c"}], ["Integer", {".class": "LiteralType", "fallback": "builtins.str", "value": "bhilqp"}], ["UnsignedInteger", {".class": "LiteralType", "fallback": "builtins.str", "value": "BHILQP"}], ["Float", {".class": "LiteralType", "fallback": "builtins.str", "value": "efdg"}], ["Complex", {".class": "LiteralType", "fallback": "builtins.str", "value": "FDG"}], ["AllInteger", {".class": "LiteralType", "fallback": "builtins.str", "value": "bBhHiIlLqQpP"}], ["AllFloat", {".class": "LiteralType", "fallback": "builtins.str", "value": "efdgFDG"}], ["Datetime", {".class": "LiteralType", "fallback": "builtins.str", "value": "Mm"}], ["All", {".class": "LiteralType", "fallback": "builtins.str", "value": "?bhilqpBHILQPefdgFDGSUVOMm"}]], "readonly_keys": [], "required_keys": ["All", "AllFloat", "AllInteger", "Character", "Complex", "Datetime", "Float", "Integer", "UnsignedInteger"]}}}, "_TypeTuple": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "numpy.core.numerictypes._TypeTuple", "line": 74, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}, "types.UnionType", {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}, "types.UnionType", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.numerictypes.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.numerictypes.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.numerictypes.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.numerictypes.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.numerictypes.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.numerictypes.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.numerictypes.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_typedict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeType", "item": "numpy.generic"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": 1, "name": "_T", "namespace": "numpy.core.numerictypes._typedict", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.dict"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.core.numerictypes._typedict", "name": "_typedict", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": 1, "name": "_T", "namespace": "numpy.core.numerictypes._typedict", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.core.numerictypes._typedict", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy.core.numerictypes", "mro": ["numpy.core.numerictypes._typedict", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.numerictypes._typedict.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": 1, "name": "_T", "namespace": "numpy.core.numerictypes._typedict", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.core.numerictypes._typedict"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of _typedict", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": 1, "name": "_T", "namespace": "numpy.core.numerictypes._typedict", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._typedict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": 1, "name": "_T", "namespace": "numpy.core.numerictypes._typedict", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.core.numerictypes._typedict"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_T"], "typeddict_type": null}}, "bool_": {".class": "SymbolTableNode", "cross_ref": "numpy.bool_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "byte": {".class": "SymbolTableNode", "cross_ref": "numpy.byte", "kind": "Gdef", "module_hidden": true, "module_public": false}, "bytes_": {".class": "SymbolTableNode", "cross_ref": "numpy.bytes_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "cast": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.numerictypes.cast", "name": "cast", "type": {".class": "Instance", "args": ["numpy.core.numerictypes._CastFunc"], "extra_attrs": null, "type_ref": "numpy.core.numerictypes._typedict"}}}, "cdouble": {".class": "SymbolTableNode", "cross_ref": "numpy.cdouble", "kind": "Gdef", "module_hidden": true, "module_public": false}, "clongdouble": {".class": "SymbolTableNode", "cross_ref": "numpy.clongdouble", "kind": "Gdef", "module_hidden": true, "module_public": false}, "csingle": {".class": "SymbolTableNode", "cross_ref": "numpy.c<PERSON>le", "kind": "Gdef", "module_hidden": true, "module_public": false}, "datetime64": {".class": "SymbolTableNode", "cross_ref": "numpy.datetime64", "kind": "Gdef", "module_hidden": true, "module_public": false}, "double": {".class": "SymbolTableNode", "cross_ref": "numpy.double", "kind": "Gdef", "module_hidden": true, "module_public": false}, "dtype": {".class": "SymbolTableNode", "cross_ref": "numpy.dtype", "kind": "Gdef", "module_hidden": true, "module_public": false}, "generic": {".class": "SymbolTableNode", "cross_ref": "numpy.generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "half": {".class": "SymbolTableNode", "cross_ref": "numpy.half", "kind": "Gdef", "module_hidden": true, "module_public": false}, "int_": {".class": "SymbolTableNode", "cross_ref": "numpy.int_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "intc": {".class": "SymbolTableNode", "cross_ref": "numpy.intc", "kind": "Gdef", "module_hidden": true, "module_public": false}, "issctype": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.numerictypes.issctype", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["rep"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.numerictypes.issctype", "name": "issctype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["rep"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "issctype", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.numerictypes.issctype", "name": "issctype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["rep"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "issctype", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["rep"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.numerictypes.issctype", "name": "issctype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["rep"], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "issctype", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.numerictypes.issctype", "name": "issctype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["rep"], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "issctype", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["rep"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "issctype", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["rep"], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "issctype", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "issubclass_": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.numerictypes.issubclass_", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["arg1", "arg2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.numerictypes.issubclass_", "name": "issubclass_", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["arg1", "arg2"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.core.numerictypes._TypeTuple"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "issubclass_", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.numerictypes.issubclass_", "name": "issubclass_", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["arg1", "arg2"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.core.numerictypes._TypeTuple"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "issubclass_", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["arg1", "arg2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.numerictypes.issubclass_", "name": "issubclass_", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["arg1", "arg2"], "arg_types": ["builtins.object", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "issubclass_", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.numerictypes.issubclass_", "name": "issubclass_", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["arg1", "arg2"], "arg_types": ["builtins.object", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "issubclass_", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["arg1", "arg2"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.core.numerictypes._TypeTuple"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "issubclass_", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["arg1", "arg2"], "arg_types": ["builtins.object", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "issubclass_", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "issubdtype": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["arg1", "arg2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.numerictypes.issubdtype", "name": "issubdtype", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["arg1", "arg2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "issubdtype", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "issubsctype": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["arg1", "arg2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.numerictypes.issubsctype", "name": "issubsctype", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["arg1", "arg2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "issubsctype", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "longdouble": {".class": "SymbolTableNode", "cross_ref": "numpy.longdouble", "kind": "Gdef", "module_hidden": true, "module_public": false}, "longlong": {".class": "SymbolTableNode", "cross_ref": "numpy.longlong", "kind": "Gdef", "module_hidden": true, "module_public": false}, "maximum_sctype": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.numerictypes.maximum_sctype", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["t"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.numerictypes.maximum_sctype", "name": "maximum_sctype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["t"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.maximum_sctype#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "maximum_sctype", "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.maximum_sctype#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.maximum_sctype#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.numerictypes.maximum_sctype", "name": "maximum_sctype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["t"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.maximum_sctype#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "maximum_sctype", "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.maximum_sctype#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.maximum_sctype#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["t"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.numerictypes.maximum_sctype", "name": "maximum_sctype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["t"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "maximum_sctype", "ret_type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.numerictypes.maximum_sctype", "name": "maximum_sctype", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["t"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "maximum_sctype", "ret_type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["t"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.maximum_sctype#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "maximum_sctype", "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.maximum_sctype#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.maximum_sctype#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["t"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "maximum_sctype", "ret_type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "nbytes": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.numerictypes.nbytes", "name": "nbytes", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "numpy.core.numerictypes._typedict"}}}, "ndarray": {".class": "SymbolTableNode", "cross_ref": "numpy.n<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "obj2sctype": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.numerictypes.obj2sctype", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["rep", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.numerictypes.obj2sctype", "name": "obj2sctype", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["rep", "default"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.obj2sctype#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obj2sctype", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.obj2sctype#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.obj2sctype#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.numerictypes.obj2sctype", "name": "obj2sctype", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["rep", "default"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.obj2sctype#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obj2sctype", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.obj2sctype#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.obj2sctype#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["rep", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.numerictypes.obj2sctype", "name": "obj2sctype", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["rep", "default"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.obj2sctype#1", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -2, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obj2sctype", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -2, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.obj2sctype#1", "upper_bound": "numpy.generic", "values": [], "variance": 0}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.obj2sctype#1", "upper_bound": "numpy.generic", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -2, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.numerictypes.obj2sctype", "name": "obj2sctype", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["rep", "default"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.obj2sctype#1", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -2, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obj2sctype", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -2, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.obj2sctype#1", "upper_bound": "numpy.generic", "values": [], "variance": 0}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.obj2sctype#1", "upper_bound": "numpy.generic", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -2, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["rep", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.numerictypes.obj2sctype", "name": "obj2sctype", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["rep", "default"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obj2sctype", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.numerictypes.obj2sctype", "name": "obj2sctype", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["rep", "default"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obj2sctype", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["rep", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.numerictypes.obj2sctype", "name": "obj2sctype", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["rep", "default"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -1, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype#3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obj2sctype", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -1, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -1, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype#3", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.numerictypes.obj2sctype", "name": "obj2sctype", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["rep", "default"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -1, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype#3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obj2sctype", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -1, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -1, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype#3", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["rep", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.numerictypes.obj2sctype", "name": "obj2sctype", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["rep", "default"], "arg_types": ["builtins.object", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obj2sctype", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.numerictypes.obj2sctype", "name": "obj2sctype", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["rep", "default"], "arg_types": ["builtins.object", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obj2sctype", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["rep", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.numerictypes.obj2sctype", "name": "obj2sctype", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["rep", "default"], "arg_types": ["builtins.object", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -1, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obj2sctype", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -1, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -1, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.numerictypes.obj2sctype", "name": "obj2sctype", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["rep", "default"], "arg_types": ["builtins.object", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -1, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obj2sctype", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -1, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -1, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["rep", "default"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.obj2sctype#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obj2sctype", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.obj2sctype#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.obj2sctype#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["rep", "default"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.obj2sctype#1", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._dtype_like._DTypeLike"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -2, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obj2sctype", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -2, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.obj2sctype#1", "upper_bound": "numpy.generic", "values": [], "variance": 0}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.core.numerictypes.obj2sctype#1", "upper_bound": "numpy.generic", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -2, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["rep", "default"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obj2sctype", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["rep", "default"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -1, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype#3", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obj2sctype", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -1, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype#3", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -1, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype#3", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["rep", "default"], "arg_types": ["builtins.object", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obj2sctype", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["rep", "default"], "arg_types": ["builtins.object", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -1, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obj2sctype", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -1, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.numerictypes._T", "id": -1, "name": "_T", "namespace": "numpy.core.numerictypes.obj2sctype", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "object_": {".class": "SymbolTableNode", "cross_ref": "numpy.object_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sctype2char": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["sctype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.numerictypes.sctype2char", "name": "sctype2char", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["sctype"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sctype2char", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sctypeDict": {".class": "SymbolTableNode", "cross_ref": "numpy.core._type_aliases.sctypeDict", "kind": "Gdef", "module_public": false}, "sctypes": {".class": "SymbolTableNode", "cross_ref": "numpy.core._type_aliases.sctypes", "kind": "Gdef", "module_public": false}, "short": {".class": "SymbolTableNode", "cross_ref": "numpy.short", "kind": "Gdef", "module_hidden": true, "module_public": false}, "single": {".class": "SymbolTableNode", "cross_ref": "numpy.single", "kind": "Gdef", "module_hidden": true, "module_public": false}, "str_": {".class": "SymbolTableNode", "cross_ref": "numpy.str_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "timedelta64": {".class": "SymbolTableNode", "cross_ref": "numpy.timedelta64", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typecodes": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.numerictypes.typecodes", "name": "typecodes", "type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.core.numerictypes._TypeCodes"}}}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ubyte": {".class": "SymbolTableNode", "cross_ref": "numpy.ubyte", "kind": "Gdef", "module_hidden": true, "module_public": false}, "uint": {".class": "SymbolTableNode", "cross_ref": "numpy.uint", "kind": "Gdef", "module_hidden": true, "module_public": false}, "uintc": {".class": "SymbolTableNode", "cross_ref": "numpy.uintc", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ulonglong": {".class": "SymbolTableNode", "cross_ref": "numpy.ul<PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ushort": {".class": "SymbolTableNode", "cross_ref": "numpy.ushort", "kind": "Gdef", "module_hidden": true, "module_public": false}, "void": {".class": "SymbolTableNode", "cross_ref": "numpy.void", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\AnacondaPath\\Lib\\site-packages\\numpy\\core\\numerictypes.pyi"}