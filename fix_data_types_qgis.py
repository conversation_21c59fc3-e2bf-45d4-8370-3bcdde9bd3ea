"""
Fix Data Types in test123.gpkg - QGIS Console Script

This script is designed to run from within QGIS Python Console.
It fixes data type issues in the test123.gpkg file where numeric fields
are incorrectly stored as text due to Danish formatting.

To use:
1. Open QGIS
2. Open Python Console (Plugins > Python Console)
3. Run: exec(open('fix_data_types_qgis.py').read())

The script will:
- Load test123 layer from test123.gpkg
- Fix data types for Danish numeric fields
- Save result as test123_fixed layer in the same GeoPackage
"""

import os
from qgis.core import QgsVectorLayer, QVariant
import processing

def fix_data_types():
    """Fix data types in test123.gpkg"""
    
    print("=" * 60)
    print("🔧 FIXING DATA TYPES IN test123.gpkg")
    print("=" * 60)
    
    # Set up file path
    current_dir = os.getcwd()
    gpkg_path = os.path.join(current_dir, "test123.gpkg")
    
    if not os.path.exists(gpkg_path):
        print(f"❌ File not found: {gpkg_path}")
        print(f"   Current directory: {current_dir}")
        return False
        
    print(f"📁 Input file: {gpkg_path}")
    
    # Load the input layer
    input_layer = QgsVectorLayer(f"{gpkg_path}|layername=test123", "test123", "ogr")
    
    if not input_layer.isValid():
        print("❌ Failed to load test123 layer")
        return False
        
    print(f"✅ Loaded layer with {input_layer.featureCount():,} features")
    
    # Analyze current field types
    print("\n📊 Current field analysis:")
    print("-" * 40)
    
    problematic_fields = []
    for field in input_layer.fields():
        field_name = field.name()
        field_type = QVariant.typeToName(field.type())
        
        # Check problematic fields
        if field_name in ['Longitude', 'Latitude', 'Areal_ha'] and field_type != 'double':
            problematic_fields.append(f"{field_name}: {field_type} → should be double")
            print(f"  ⚠️  {field_name}: {field_type} → should be double")
        elif field_name in ['Postnr', 'Primær ejer alder', 'Opførelsesår'] and field_type != 'int':
            problematic_fields.append(f"{field_name}: {field_type} → should be int")
            print(f"  ⚠️  {field_name}: {field_type} → should be int")
        else:
            print(f"  ✅  {field_name}: {field_type}")
    
    if not problematic_fields:
        print("\n✅ No data type issues found!")
        return True
        
    print(f"\n🔍 Found {len(problematic_fields)} fields with data type issues")
    
    # Create field mappings
    field_mappings = []
    
    print("\n🔧 Creating field mappings:")
    
    for field in input_layer.fields():
        field_name = field.name()
        
        if field_name == 'Longitude':
            # Danish decimal format to double
            field_mapping = {
                'name': 'Longitude',
                'type': QVariant.Double,
                'length': field.length(),
                'precision': 6,
                'expression': 'CASE WHEN "Longitude" IS NULL OR trim("Longitude") = \'\' THEN NULL ELSE to_real(replace(trim("Longitude"), \',\', \'.\')) END',
                'alias': '',
                'comment': '',
                'sub_type': 0,
                'type_name': 'double'
            }
            print("  🔄 Longitude: String → Double (Danish format)")
            
        elif field_name == 'Latitude':
            # Danish decimal format to double
            field_mapping = {
                'name': 'Latitude',
                'type': QVariant.Double,
                'length': field.length(),
                'precision': 6,
                'expression': 'CASE WHEN "Latitude" IS NULL OR trim("Latitude") = \'\' THEN NULL ELSE to_real(replace(trim("Latitude"), \',\', \'.\')) END',
                'alias': '',
                'comment': '',
                'sub_type': 0,
                'type_name': 'double'
            }
            print("  🔄 Latitude: String → Double (Danish format)")
            
        elif field_name == 'Areal_ha':
            # Danish decimal format to double
            field_mapping = {
                'name': 'Areal_ha',
                'type': QVariant.Double,
                'length': field.length(),
                'precision': 2,
                'expression': 'CASE WHEN "Areal_ha" IS NULL OR trim("Areal_ha") = \'\' THEN NULL ELSE to_real(replace(trim("Areal_ha"), \',\', \'.\')) END',
                'alias': '',
                'comment': '',
                'sub_type': 0,
                'type_name': 'double'
            }
            print("  🔄 Areal_ha: String → Double (Danish format)")
            
        elif field_name == 'Postnr':
            # String to integer
            field_mapping = {
                'name': 'Postnr',
                'type': QVariant.Int,
                'length': field.length(),
                'precision': 0,
                'expression': 'CASE WHEN "Postnr" IS NULL OR trim("Postnr") = \'\' OR trim("Postnr") = \'-\' THEN NULL ELSE to_int(trim("Postnr")) END',
                'alias': '',
                'comment': '',
                'sub_type': 0,
                'type_name': 'int'
            }
            print("  🔄 Postnr: String → Integer")
            
        elif field_name == 'Primær ejer alder':
            # String to integer
            field_mapping = {
                'name': 'Primær ejer alder',
                'type': QVariant.Int,
                'length': field.length(),
                'precision': 0,
                'expression': 'CASE WHEN "Primær ejer alder" IS NULL OR trim("Primær ejer alder") = \'\' OR trim("Primær ejer alder") = \'-\' THEN NULL ELSE to_int(trim("Primær ejer alder")) END',
                'alias': '',
                'comment': '',
                'sub_type': 0,
                'type_name': 'int'
            }
            print("  🔄 Primær ejer alder: String → Integer")
            
        elif field_name == 'Opførelsesår':
            # String to integer
            field_mapping = {
                'name': 'Opførelsesår',
                'type': QVariant.Int,
                'length': field.length(),
                'precision': 0,
                'expression': 'CASE WHEN "Opførelsesår" IS NULL OR trim("Opførelsesår") = \'\' OR trim("Opførelsesår") = \'-\' THEN NULL ELSE to_int(trim("Opførelsesår")) END',
                'alias': '',
                'comment': '',
                'sub_type': 0,
                'type_name': 'int'
            }
            print("  🔄 Opførelsesår: String → Integer")
            
        else:
            # Keep existing field as-is
            field_mapping = {
                'name': field_name,
                'type': field.type(),
                'length': field.length(),
                'precision': field.precision(),
                'expression': f'"{field_name}"',
                'alias': '',
                'comment': '',
                'sub_type': 0,
                'type_name': QVariant.typeToName(field.type())
            }
            print(f"  ✅ {field_name}: Keeping as {QVariant.typeToName(field.type())}")
            
        field_mappings.append(field_mapping)
    
    # Apply field refactoring
    output_path = f"{gpkg_path}|layername=test123_fixed"
    
    print("\n🔧 Applying data type fixes...")
    
    try:
        result = processing.run(
            'native:refactorfields',
            {
                'INPUT': input_layer,
                'FIELDS_MAPPING': field_mappings,
                'OUTPUT': output_path
            }
        )
        
        if result and 'OUTPUT' in result:
            print("✅ Data type fixes completed successfully")
            print(f"📁 Output saved as layer 'test123_fixed' in {gpkg_path}")
            
            # Validate results
            validate_results(gpkg_path)
            return True
        else:
            print("❌ Failed to create output")
            return False
            
    except Exception as e:
        print(f"❌ Error during processing: {str(e)}")
        return False

def validate_results(gpkg_path):
    """Validate the results of the data type fixing"""
    
    print("\n🔍 Validating results:")
    print("-" * 30)
    
    try:
        # Load the output layer
        output_layer = QgsVectorLayer(f"{gpkg_path}|layername=test123_fixed", "test123_fixed", "ogr")
        
        if not output_layer.isValid():
            print("❌ Could not load output layer for validation")
            return
            
        print(f"✅ Output layer loaded with {output_layer.featureCount():,} features")
        
        # Check field types
        target_fields = {
            'Longitude': 'double',
            'Latitude': 'double', 
            'Areal_ha': 'double',
            'Postnr': 'int',
            'Primær ejer alder': 'int',
            'Opførelsesår': 'int'
        }
        
        all_correct = True
        for field in output_layer.fields():
            field_name = field.name()
            if field_name in target_fields:
                actual_type = QVariant.typeToName(field.type()).lower()
                expected_type = target_fields[field_name]
                
                if actual_type == expected_type:
                    print(f"  ✅ {field_name}: {actual_type}")
                else:
                    print(f"  ❌ {field_name}: {actual_type} (expected {expected_type})")
                    all_correct = False
                    
        if all_correct:
            print("\n🎉 All target fields have correct data types!")
        else:
            print("\n⚠️  Some fields still have incorrect data types")
            
    except Exception as e:
        print(f"❌ Error during validation: {str(e)}")

# Run the fix
if __name__ == "__main__":
    success = fix_data_types()
    if success:
        print("\n🎉 Data type fixing completed successfully!")
        print("📊 You can now use the 'test123_fixed' layer for analysis")
    else:
        print("\n❌ Data type fixing failed")
else:
    # When imported/executed in QGIS console
    success = fix_data_types()
    if success:
        print("\n🎉 Data type fixing completed successfully!")
        print("📊 You can now use the 'test123_fixed' layer for analysis")
