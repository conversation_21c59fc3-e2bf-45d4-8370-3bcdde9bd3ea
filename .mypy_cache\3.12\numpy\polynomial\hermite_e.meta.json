{"data_mtime": 1749245427, "dep_lines": [4, 5, 1, 3, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 30, 30], "dependencies": ["numpy.polynomial._polybase", "numpy.polynomial.polyutils", "typing", "numpy", "builtins", "_frozen_importlib", "abc"], "hash": "2f62f51674e8991f9d6a8e08ec303b3001ed133b", "id": "numpy.polynomial.hermite_e", "ignore_all": true, "interface_hash": "5fc4c92e5836ddc1646b32c14a64dcd098a45fef", "mtime": 1707226025, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\AnacondaPath\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.pyi", "plugin_data": null, "size": 1238, "suppressed": [], "version_id": "1.15.0"}