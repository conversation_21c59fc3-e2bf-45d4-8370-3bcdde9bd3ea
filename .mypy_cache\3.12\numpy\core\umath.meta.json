{"data_mtime": 1749245417, "dep_lines": [9, 1, 1, 1, 1, 10], "dep_prios": [5, 5, 30, 30, 30, 5], "dependencies": ["numpy.core", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "07d3af7fa4727fa2bb02de0dec40ca5d2d356cf1", "id": "numpy.core.umath", "ignore_all": true, "interface_hash": "fdb28dbb29667ada2dfe2d0b7b278d41cbf3c1cb", "mtime": 1707226024, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\AnacondaPath\\Lib\\site-packages\\numpy\\core\\umath.py", "plugin_data": null, "size": 2040, "suppressed": ["numpy.core._multiarray_umath"], "version_id": "1.15.0"}