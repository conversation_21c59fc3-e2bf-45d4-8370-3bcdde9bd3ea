{"data_mtime": 1749245427, "dep_lines": [6, 3, 4, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["numpy._typing._array_like", "re", "textwrap", "builtins", "_collections_abc", "_frozen_importlib", "abc", "numpy._typing._dtype_like", "typing"], "hash": "8253f4257a68231753e23839353e07f1f36f3b8d", "id": "numpy._typing._add_docstring", "ignore_all": true, "interface_hash": "f35f834bbf060f244ac0a9cca65dc3d82f8ff0fb", "mtime": 1707226024, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\AnacondaPath\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py", "plugin_data": null, "size": 3922, "suppressed": [], "version_id": "1.15.0"}