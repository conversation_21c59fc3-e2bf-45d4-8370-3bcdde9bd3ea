{".class": "MypyFile", "_fullname": "numpy.lib.arraysetops", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "L": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NDArray": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.NDArray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SupportsIndex": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsIndex", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeBool_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeBool_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeDT64_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeDT64_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeNumber_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeNumber_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeObject_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeObject_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeTD64_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeTD64_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_NumberType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._NumberType", "name": "_NumberType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}}, "_SCT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "name": "_SCT", "upper_bound": "numpy.generic", "values": [], "variance": 0}}, "_SCTNoCast": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "name": "_SCTNoCast", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.arraysetops.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.arraysetops.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.arraysetops.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.arraysetops.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.arraysetops.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.arraysetops.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.arraysetops.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "bool_": {".class": "SymbolTableNode", "cross_ref": "numpy.bool_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "byte": {".class": "SymbolTableNode", "cross_ref": "numpy.byte", "kind": "Gdef", "module_hidden": true, "module_public": false}, "bytes_": {".class": "SymbolTableNode", "cross_ref": "numpy.bytes_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "cdouble": {".class": "SymbolTableNode", "cross_ref": "numpy.cdouble", "kind": "Gdef", "module_hidden": true, "module_public": false}, "clongdouble": {".class": "SymbolTableNode", "cross_ref": "numpy.clongdouble", "kind": "Gdef", "module_hidden": true, "module_public": false}, "csingle": {".class": "SymbolTableNode", "cross_ref": "numpy.c<PERSON>le", "kind": "Gdef", "module_hidden": true, "module_public": false}, "datetime64": {".class": "SymbolTableNode", "cross_ref": "numpy.datetime64", "kind": "Gdef", "module_hidden": true, "module_public": false}, "double": {".class": "SymbolTableNode", "cross_ref": "numpy.double", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ediff1d": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib.arraysetops.ediff1d", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.ediff1d", "name": "ediff1d", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.ediff1d", "name": "ediff1d", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.ediff1d", "name": "ediff1d", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy.lib.arraysetops.ediff1d#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy.lib.arraysetops.ediff1d#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy.lib.arraysetops.ediff1d#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.ediff1d", "name": "ediff1d", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy.lib.arraysetops.ediff1d#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy.lib.arraysetops.ediff1d#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy.lib.arraysetops.ediff1d#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.ediff1d", "name": "ediff1d", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.ediff1d", "name": "ediff1d", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.ediff1d", "name": "ediff1d", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeDT64_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeTD64_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": ["numpy.timedelta64"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.ediff1d", "name": "ediff1d", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeDT64_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeTD64_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": ["numpy.timedelta64"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.ediff1d", "name": "ediff1d", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeObject_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.ediff1d", "name": "ediff1d", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeObject_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy.lib.arraysetops.ediff1d#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy.lib.arraysetops.ediff1d#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy.lib.arraysetops.ediff1d#1", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeDT64_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeTD64_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": ["numpy.timedelta64"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["ary", "to_end", "to_begin"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeObject_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ediff1d", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "generic": {".class": "SymbolTableNode", "cross_ref": "numpy.generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "half": {".class": "SymbolTableNode", "cross_ref": "numpy.half", "kind": "Gdef", "module_hidden": true, "module_public": false}, "in1d": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "invert"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib.arraysetops.in1d", "name": "in1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "invert"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "in1d", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "int8": {".class": "SymbolTableNode", "cross_ref": "numpy.int8", "kind": "Gdef", "module_hidden": true, "module_public": false}, "int_": {".class": "SymbolTableNode", "cross_ref": "numpy.int_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "intc": {".class": "SymbolTableNode", "cross_ref": "numpy.intc", "kind": "Gdef", "module_hidden": true, "module_public": false}, "intersect1d": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib.arraysetops.intersect1d", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.intersect1d", "name": "intersect1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.intersect1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.intersect1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intersect1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.intersect1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.intersect1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.intersect1d", "name": "intersect1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.intersect1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.intersect1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intersect1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.intersect1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.intersect1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.intersect1d", "name": "intersect1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intersect1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.intersect1d", "name": "intersect1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intersect1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.intersect1d", "name": "intersect1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.intersect1d#2", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.intersect1d#2", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intersect1d", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.intersect1d#2", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.intersect1d#2", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.intersect1d", "name": "intersect1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.intersect1d#2", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.intersect1d#2", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intersect1d", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.intersect1d#2", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.intersect1d#2", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.intersect1d", "name": "intersect1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intersect1d", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.intersect1d", "name": "intersect1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intersect1d", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.intersect1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.intersect1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intersect1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.intersect1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.intersect1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intersect1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.intersect1d#2", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.intersect1d#2", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intersect1d", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.intersect1d#2", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.intersect1d#2", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["ar1", "ar2", "assume_unique", "return_indices"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intersect1d", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "intp": {".class": "SymbolTableNode", "cross_ref": "numpy.intp", "kind": "Gdef", "module_hidden": true, "module_public": false}, "isin": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["element", "test_elements", "assume_unique", "invert", "kind"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib.arraysetops.isin", "name": "isin", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["element", "test_elements", "assume_unique", "invert", "kind"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isin", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "longdouble": {".class": "SymbolTableNode", "cross_ref": "numpy.longdouble", "kind": "Gdef", "module_hidden": true, "module_public": false}, "longlong": {".class": "SymbolTableNode", "cross_ref": "numpy.longlong", "kind": "Gdef", "module_hidden": true, "module_public": false}, "number": {".class": "SymbolTableNode", "cross_ref": "numpy.number", "kind": "Gdef", "module_hidden": true, "module_public": false}, "object_": {".class": "SymbolTableNode", "cross_ref": "numpy.object_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "setdiff1d": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib.arraysetops.setdiff1d", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.setdiff1d", "name": "setdiff1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.setdiff1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.setdiff1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setdiff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.setdiff1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.setdiff1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.setdiff1d", "name": "setdiff1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.setdiff1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.setdiff1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setdiff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.setdiff1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.setdiff1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.setdiff1d", "name": "setdiff1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setdiff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.setdiff1d", "name": "setdiff1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setdiff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.setdiff1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.setdiff1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setdiff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.setdiff1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.setdiff1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setdiff1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "setxor1d": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib.arraysetops.setxor1d", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.setxor1d", "name": "setxor1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.setxor1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.setxor1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setxor1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.setxor1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.setxor1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.setxor1d", "name": "setxor1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.setxor1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.setxor1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setxor1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.setxor1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.setxor1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.setxor1d", "name": "setxor1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setxor1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.setxor1d", "name": "setxor1d", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setxor1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.setxor1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.setxor1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setxor1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.setxor1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.setxor1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["ar1", "ar2", "assume_unique"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setxor1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "short": {".class": "SymbolTableNode", "cross_ref": "numpy.short", "kind": "Gdef", "module_hidden": true, "module_public": false}, "single": {".class": "SymbolTableNode", "cross_ref": "numpy.single", "kind": "Gdef", "module_hidden": true, "module_public": false}, "str_": {".class": "SymbolTableNode", "cross_ref": "numpy.str_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "timedelta64": {".class": "SymbolTableNode", "cross_ref": "numpy.timedelta64", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ubyte": {".class": "SymbolTableNode", "cross_ref": "numpy.ubyte", "kind": "Gdef", "module_hidden": true, "module_public": false}, "uint": {".class": "SymbolTableNode", "cross_ref": "numpy.uint", "kind": "Gdef", "module_hidden": true, "module_public": false}, "uintc": {".class": "SymbolTableNode", "cross_ref": "numpy.uintc", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ulonglong": {".class": "SymbolTableNode", "cross_ref": "numpy.ul<PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "union1d": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib.arraysetops.union1d", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["ar1", "ar2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.union1d", "name": "union1d", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ar1", "ar2"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.union1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.union1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "union1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.union1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.union1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.union1d", "name": "union1d", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ar1", "ar2"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.union1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.union1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "union1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.union1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.union1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["ar1", "ar2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.union1d", "name": "union1d", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ar1", "ar2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "union1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.union1d", "name": "union1d", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ar1", "ar2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "union1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ar1", "ar2"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.union1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.union1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "union1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.union1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCTNoCast", "id": -1, "name": "_SCTNoCast", "namespace": "numpy.lib.arraysetops.union1d#0", "upper_bound": "builtins.object", "values": ["numpy.bool_", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ushort"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ubyte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uintc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.uint"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.ul<PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.short"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.byte"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.intc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longlong"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.half"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.single"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.double"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.c<PERSON>le"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.cdouble"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}, "numpy.timedelta64", "numpy.datetime64", "numpy.object_", "numpy.str_", "numpy.bytes_", "numpy.void"], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ar1", "ar2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "union1d", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "unique": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib.arraysetops.unique", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#2", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#2", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#2", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#2", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#2", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#2", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#4", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#4", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#4", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#4", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#4", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#4", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#6", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#6", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#6", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#6", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#6", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#6", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#8", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#8", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#8", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#8", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#8", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#8", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#10", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#10", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#10", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#10", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#10", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#10", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#12", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#12", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#12", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#12", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#12", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#12", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#14", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#14", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#14", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#14", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#14", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#14", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.arraysetops.unique", "name": "unique", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#2", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#2", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#2", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#4", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#4", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#4", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#6", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#6", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#6", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#8", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#8", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#8", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#10", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#10", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#10", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#12", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#12", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#12", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#14", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#14", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.arraysetops._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.arraysetops.unique#14", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["ar", "return_index", "return_inverse", "return_counts", "axis", "equal_nan"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unique", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.intp"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "ushort": {".class": "SymbolTableNode", "cross_ref": "numpy.ushort", "kind": "Gdef", "module_hidden": true, "module_public": false}, "void": {".class": "SymbolTableNode", "cross_ref": "numpy.void", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\AnacondaPath\\Lib\\site-packages\\numpy\\lib\\arraysetops.pyi"}