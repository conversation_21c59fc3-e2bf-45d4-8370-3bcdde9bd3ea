{"data_mtime": 1749245426, "dep_lines": [44, 3, 49, 1, 2, 4, 14, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["numpy.core._type_aliases", "collections.abc", "numpy._typing", "sys", "types", "typing", "numpy", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nested_sequence"], "hash": "d5a2e0eeab1a93029b6a2e9295b51f07cbb06c34", "id": "numpy.core.numerictypes", "ignore_all": true, "interface_hash": "cded9a9d1c5ba4e429310ac335b5c9b88b1a2f9c", "mtime": 1707226024, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\AnacondaPath\\Lib\\site-packages\\numpy\\core\\numerictypes.pyi", "plugin_data": null, "size": 3267, "suppressed": [], "version_id": "1.15.0"}