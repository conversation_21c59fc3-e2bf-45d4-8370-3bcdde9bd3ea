{".class": "MypyFile", "_fullname": "numpy.lib.format", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ARRAY_ALIGN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.format.ARRAY_ALIGN", "name": "ARRAY_ALIGN", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 64}}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BUFFER_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.format.BUFFER_SIZE", "name": "BUFFER_SIZE", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 262144}}}, "EXPECTED_KEYS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.lib.format.EXPECTED_KEYS", "name": "EXPECTED_KEYS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MAGIC_LEN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.format.MAGIC_LEN", "name": "MAGIC_LEN", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 8}}}, "MAGIC_PREFIX": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.lib.format.MAGIC_PREFIX", "name": "MAGIC_PREFIX", "type": "builtins.bytes"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.format.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.format.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.format.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.format.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.format.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.format.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.format.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "descr_to_dtype": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["descr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib.format.descr_to_dtype", "name": "descr_to_dtype", "type": null}}, "dtype_to_descr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib.format.dtype_to_descr", "name": "dtype_to_descr", "type": null}}, "header_data_from_array_1_0": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["array"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib.format.header_data_from_array_1_0", "name": "header_data_from_array_1_0", "type": null}}, "magic": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["major", "minor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib.format.magic", "name": "magic", "type": null}}, "open_memmap": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["filename", "mode", "dtype", "shape", "fortran_order", "version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib.format.open_memmap", "name": "open_memmap", "type": null}}, "read_array": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["fp", "allow_pickle", "pickle_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib.format.read_array", "name": "read_array", "type": null}}, "read_array_header_1_0": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["fp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib.format.read_array_header_1_0", "name": "read_array_header_1_0", "type": null}}, "read_array_header_2_0": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["fp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib.format.read_array_header_2_0", "name": "read_array_header_2_0", "type": null}}, "read_magic": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["fp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib.format.read_magic", "name": "read_magic", "type": null}}, "write_array": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["fp", "array", "version", "allow_pickle", "pickle_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib.format.write_array", "name": "write_array", "type": null}}, "write_array_header_1_0": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["fp", "d"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib.format.write_array_header_1_0", "name": "write_array_header_1_0", "type": null}}, "write_array_header_2_0": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["fp", "d"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib.format.write_array_header_2_0", "name": "write_array_header_2_0", "type": null}}}, "path": "c:\\AnacondaPath\\Lib\\site-packages\\numpy\\lib\\format.pyi"}