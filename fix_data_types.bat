@echo off
echo ======================================================
echo FIXING DATA TYPES IN test123.gpkg
echo ======================================================

REM Check if input file exists
if not exist "test123.gpkg" (
    echo ERROR: test123.gpkg not found in current directory
    pause
    exit /b 1
)

echo Input file: test123.gpkg

REM Create output with corrected data types using ogr2ogr
echo.
echo Creating test123_fixed.gpkg with corrected data types...

ogr2ogr -f GPKG test123_fixed.gpkg test123.gpkg test123 ^
-sql "SELECT fid, geom, ^
[BFE-nummer], ^
JordstykkeID, ^
Matrikelnr, ^
Ejerlavsnavn, ^
CAST(replace(trim(Longitude), ',', '.') AS REAL) AS longitude, ^
CAST(replace(trim(Latitude), ',', '.') AS REAL) AS latitude, ^
[Antal ejere], ^
[Primær ejer], ^
CASE WHEN [Primær ejer alder] IS NULL OR trim([Primær ejer alder]) = '' OR trim([Primær ejer alder]) = '-' THEN NULL ELSE CAST(trim([Primær ejer alder]) AS INTEGER) END AS primær_ejer_alder, ^
[Postlinje 1], ^
CASE WHEN Postnr IS NULL OR trim(Postnr) = '' OR trim(Postnr) = '-' THEN NULL ELSE CAST(trim(Postnr) AS INTEGER) END AS postnr, ^
By, ^
Anvendelse, ^
Type, ^
Undertype, ^
CASE WHEN [Opførelsesår] IS NULL OR trim([Opførelsesår]) = '' OR trim([Opførelsesår]) = '-' THEN NULL ELSE CAST(trim([Opførelsesår]) AS INTEGER) END AS opførelsesår, ^
[Energimærke], ^
[Er fælleslod], ^
[Seneste handelspris], ^
[Seneste handelsdato], ^
[Offentlig ejendomsværdi (Ny)], ^
[Realiseret bebyggelsesprocent], ^
Grundareal, ^
CAST(replace(trim(Areal_ha), ',', '.') AS REAL) AS areal_ha, ^
Vejareal, ^
Fredskovsareal, ^
[Fredskovsareal omfang], ^
Strandbeskyttelsesareal, ^
[Strandbeskyttelse omfang] ^
FROM test123"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo SUCCESS: Data type fixes completed!
    echo Output saved to: test123_fixed.gpkg
    echo.
    echo Validating results...
    
    REM Validate the output
    ogrinfo -so test123_fixed.gpkg test123_fixed
    
    echo.
    echo ======================================================
    echo SUMMARY:
    echo - Original data: test123.gpkg (test123 layer)
    echo - Fixed data: test123_fixed.gpkg (test123_fixed layer)
    echo.
    echo Fixed fields:
    echo - longitude: String to Real (Danish decimal format)
    echo - latitude: String to Real (Danish decimal format)  
    echo - areal_ha: String to Real (Danish decimal format)
    echo - postnr: String to Integer
    echo - primær_ejer_alder: String to Integer
    echo - opførelsesår: String to Integer
    echo ======================================================
) else (
    echo.
    echo ERROR: Data type fixing failed!
    echo Check the error messages above.
)

pause
