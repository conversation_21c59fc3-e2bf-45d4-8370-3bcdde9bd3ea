{".class": "MypyFile", "_fullname": "numpy.core.def<PERSON><PERSON><PERSON>", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "L": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NDArray": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.NDArray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "S_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeBytes_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "U_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeStr_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CharArray": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.defchararray._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.core.defchararray._CharArray", "upper_bound": "builtins.object", "values": ["numpy.str_", "numpy.bytes_"], "variance": 0}], "column": 0, "fullname": "numpy.core.defchararray._CharArray", "line": 30, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.defchararray._SCT", "id": 1, "name": "_SCT", "namespace": "numpy.core.defchararray._CharArray", "upper_bound": "builtins.object", "values": ["numpy.str_", "numpy.bytes_"], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.chararray"}}}, "_OrderKACF": {".class": "SymbolTableNode", "cross_ref": "numpy._OrderKACF", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_SCT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core.defchararray._SCT", "name": "_SCT", "upper_bound": "builtins.object", "values": ["numpy.str_", "numpy.bytes_"], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.defchararray.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.defchararray.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.defchararray.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.defchararray.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.defchararray.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.defchararray.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.defchararray.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.add", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "array": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.array", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["obj", "itemsize", "copy", "unicode", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.array", "name": "array", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["obj", "itemsize", "copy", "unicode", "order"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy.core.defchararray._CharArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.array", "name": "array", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["obj", "itemsize", "copy", "unicode", "order"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy.core.defchararray._CharArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["obj", "itemsize", "copy", "unicode", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.array", "name": "array", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["obj", "itemsize", "copy", "unicode", "order"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy.core.defchararray._CharArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.array", "name": "array", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["obj", "itemsize", "copy", "unicode", "order"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy.core.defchararray._CharArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["obj", "itemsize", "copy", "unicode", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.array", "name": "array", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["obj", "itemsize", "copy", "unicode", "order"], "arg_types": ["builtins.object", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy.core.defchararray._CharArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.array", "name": "array", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["obj", "itemsize", "copy", "unicode", "order"], "arg_types": ["builtins.object", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy.core.defchararray._CharArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["obj", "itemsize", "copy", "unicode", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.array", "name": "array", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["obj", "itemsize", "copy", "unicode", "order"], "arg_types": ["builtins.object", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy.core.defchararray._CharArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.array", "name": "array", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["obj", "itemsize", "copy", "unicode", "order"], "arg_types": ["builtins.object", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy.core.defchararray._CharArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["obj", "itemsize", "copy", "unicode", "order"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy.core.defchararray._CharArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["obj", "itemsize", "copy", "unicode", "order"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy.core.defchararray._CharArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["obj", "itemsize", "copy", "unicode", "order"], "arg_types": ["builtins.object", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy.core.defchararray._CharArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["obj", "itemsize", "copy", "unicode", "order"], "arg_types": ["builtins.object", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy.core.defchararray._CharArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "asarray": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.asarray", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["obj", "itemsize", "unicode", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.asarray", "name": "asarray", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["obj", "itemsize", "unicode", "order"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "asarray", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy.core.defchararray._CharArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.asarray", "name": "asarray", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["obj", "itemsize", "unicode", "order"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "asarray", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy.core.defchararray._CharArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["obj", "itemsize", "unicode", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.asarray", "name": "asarray", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["obj", "itemsize", "unicode", "order"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "asarray", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy.core.defchararray._CharArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.asarray", "name": "asarray", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["obj", "itemsize", "unicode", "order"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "asarray", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy.core.defchararray._CharArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["obj", "itemsize", "unicode", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.asarray", "name": "asarray", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["obj", "itemsize", "unicode", "order"], "arg_types": ["builtins.object", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "asarray", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy.core.defchararray._CharArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.asarray", "name": "asarray", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["obj", "itemsize", "unicode", "order"], "arg_types": ["builtins.object", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "asarray", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy.core.defchararray._CharArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["obj", "itemsize", "unicode", "order"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.asarray", "name": "asarray", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["obj", "itemsize", "unicode", "order"], "arg_types": ["builtins.object", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "asarray", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy.core.defchararray._CharArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.asarray", "name": "asarray", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["obj", "itemsize", "unicode", "order"], "arg_types": ["builtins.object", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "asarray", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy.core.defchararray._CharArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["obj", "itemsize", "unicode", "order"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "asarray", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy.core.defchararray._CharArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["obj", "itemsize", "unicode", "order"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "asarray", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy.core.defchararray._CharArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["obj", "itemsize", "unicode", "order"], "arg_types": ["builtins.object", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "asarray", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy.core.defchararray._CharArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["obj", "itemsize", "unicode", "order"], "arg_types": ["builtins.object", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "asarray", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy.core.defchararray._CharArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "b_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeBool_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "bool_": {".class": "SymbolTableNode", "cross_ref": "numpy.bool_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "bytes_": {".class": "SymbolTableNode", "cross_ref": "numpy.bytes_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "capitalize": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.capitalize", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.capitalize", "name": "capitalize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "capitalize", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.capitalize", "name": "capitalize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "capitalize", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.capitalize", "name": "capitalize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "capitalize", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.capitalize", "name": "capitalize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "capitalize", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "capitalize", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "capitalize", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "center": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.center", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["a", "width", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.center", "name": "center", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "width", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "center", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.center", "name": "center", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "width", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "center", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["a", "width", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.center", "name": "center", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "width", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "center", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.center", "name": "center", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "width", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "center", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "width", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "center", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "width", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "center", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "chararray": {".class": "SymbolTableNode", "cross_ref": "numpy.chararray", "kind": "Gdef", "module_public": false}, "compare_chararrays": {".class": "SymbolTableNode", "cross_ref": "numpy.core.multiarray.compare_chararrays", "kind": "Gdef", "module_public": false}, "count": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.count", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.count", "name": "count", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "count", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.count", "name": "count", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "count", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.count", "name": "count", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "count", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.count", "name": "count", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "count", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "count", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "count", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "decode": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["a", "encoding", "errors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.decode", "name": "decode", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["a", "encoding", "errors"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decode", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dtype": {".class": "SymbolTableNode", "cross_ref": "numpy.dtype", "kind": "Gdef", "module_hidden": true, "module_public": false}, "encode": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["a", "encoding", "errors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.encode", "name": "encode", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["a", "encoding", "errors"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encode", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "endswith": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.endswith", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "suffix", "start", "end"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.endswith", "name": "endswith", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "suffix", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "endswith", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.endswith", "name": "endswith", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "suffix", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "endswith", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "suffix", "start", "end"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.endswith", "name": "endswith", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "suffix", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "endswith", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.endswith", "name": "endswith", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "suffix", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "endswith", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "suffix", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "endswith", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "suffix", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "endswith", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "equal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.equal", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.equal", "name": "equal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "equal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.equal", "name": "equal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "equal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.equal", "name": "equal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "equal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.equal", "name": "equal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "equal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "equal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "equal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "expandtabs": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.expandtabs", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "tabsize"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.expandtabs", "name": "expandtabs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "tabsize"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expandtabs", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.expandtabs", "name": "expandtabs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "tabsize"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expandtabs", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "tabsize"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.expandtabs", "name": "expandtabs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "tabsize"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expandtabs", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.expandtabs", "name": "expandtabs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "tabsize"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expandtabs", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "tabsize"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expandtabs", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "tabsize"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expandtabs", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "find": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.find", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.find", "name": "find", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.find", "name": "find", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.find", "name": "find", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.find", "name": "find", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "greater": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.greater", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.greater", "name": "greater", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "greater", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.greater", "name": "greater", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "greater", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.greater", "name": "greater", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "greater", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.greater", "name": "greater", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "greater", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "greater", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "greater", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "greater_equal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.greater_equal", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.greater_equal", "name": "greater_equal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "greater_equal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.greater_equal", "name": "greater_equal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "greater_equal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.greater_equal", "name": "greater_equal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "greater_equal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.greater_equal", "name": "greater_equal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "greater_equal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "greater_equal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "greater_equal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "i_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeInt_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "index": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.index", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.index", "name": "index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "index", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.index", "name": "index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "index", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.index", "name": "index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "index", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.index", "name": "index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "index", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "index", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "index", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "int_": {".class": "SymbolTableNode", "cross_ref": "numpy.int_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "isalnum": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.isalnum", "name": "isalnum", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isalnum", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isalpha": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.isalpha", "name": "isalpha", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isalpha", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isdecimal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.isdecimal", "name": "isdecimal", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isdecimal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isdigit": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.isdigit", "name": "isdigit", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isdigit", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "islower": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.islower", "name": "islower", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "islower", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isnumeric": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.isnumeric", "name": "isnumeric", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isnumeric", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isspace": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.isspace", "name": "isspace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isspace", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "istitle": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.istitle", "name": "istitle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "istitle", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isupper": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.isupper", "name": "isupper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isupper", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "join": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.join", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["sep", "seq"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.join", "name": "join", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["sep", "seq"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "join", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.join", "name": "join", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["sep", "seq"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "join", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["sep", "seq"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.join", "name": "join", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["sep", "seq"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "join", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.join", "name": "join", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["sep", "seq"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "join", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["sep", "seq"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "join", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["sep", "seq"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "join", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "less": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.less", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.less", "name": "less", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "less", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.less", "name": "less", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "less", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.less", "name": "less", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "less", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.less", "name": "less", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "less", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "less", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "less", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "less_equal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.less_equal", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.less_equal", "name": "less_equal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "less_equal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.less_equal", "name": "less_equal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "less_equal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.less_equal", "name": "less_equal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "less_equal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.less_equal", "name": "less_equal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "less_equal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "less_equal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "less_equal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "ljust": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.ljust", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["a", "width", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.ljust", "name": "ljust", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "width", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ljust", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.ljust", "name": "ljust", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "width", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ljust", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["a", "width", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.ljust", "name": "ljust", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "width", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ljust", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.ljust", "name": "ljust", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "width", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ljust", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "width", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ljust", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "width", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ljust", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "lower": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.lower", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.lower", "name": "lower", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lower", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.lower", "name": "lower", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lower", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.lower", "name": "lower", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lower", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.lower", "name": "lower", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lower", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lower", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lower", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "lstrip": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.lstrip", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "chars"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.lstrip", "name": "lstrip", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "chars"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lstrip", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.lstrip", "name": "lstrip", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "chars"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lstrip", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "chars"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.lstrip", "name": "lstrip", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "chars"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lstrip", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.lstrip", "name": "lstrip", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "chars"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lstrip", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "chars"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lstrip", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "chars"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lstrip", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "mod": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.mod", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.mod", "name": "mod", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "value"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mod", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.mod", "name": "mod", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "value"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mod", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.mod", "name": "mod", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "value"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mod", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.mod", "name": "mod", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "value"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mod", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "value"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mod", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "value"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mod", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "multiply": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.multiply", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "i"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.multiply", "name": "multiply", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "i"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "multiply", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.multiply", "name": "multiply", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "i"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "multiply", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "i"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.multiply", "name": "multiply", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "i"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "multiply", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.multiply", "name": "multiply", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "i"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "multiply", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "i"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "multiply", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "i"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "multiply", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "not_equal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.not_equal", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.not_equal", "name": "not_equal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "not_equal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.not_equal", "name": "not_equal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "not_equal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.not_equal", "name": "not_equal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "not_equal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.not_equal", "name": "not_equal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "not_equal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "not_equal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x1", "x2"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "not_equal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "object_": {".class": "SymbolTableNode", "cross_ref": "numpy.object_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "partition": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.partition", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "sep"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.partition", "name": "partition", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "sep"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "partition", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.partition", "name": "partition", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "sep"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "partition", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "sep"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.partition", "name": "partition", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "sep"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "partition", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.partition", "name": "partition", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "sep"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "partition", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "sep"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "partition", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "sep"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "partition", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "replace": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.replace", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["a", "old", "new", "count"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.replace", "name": "replace", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["a", "old", "new", "count"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.replace", "name": "replace", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["a", "old", "new", "count"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["a", "old", "new", "count"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.replace", "name": "replace", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["a", "old", "new", "count"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.replace", "name": "replace", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["a", "old", "new", "count"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["a", "old", "new", "count"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["a", "old", "new", "count"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "rfind": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.rfind", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.rfind", "name": "rfind", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rfind", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.rfind", "name": "rfind", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rfind", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.rfind", "name": "rfind", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rfind", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.rfind", "name": "rfind", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rfind", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rfind", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rfind", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "rindex": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.rindex", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.rindex", "name": "rindex", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rindex", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.rindex", "name": "rindex", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rindex", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.rindex", "name": "rindex", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rindex", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.rindex", "name": "rindex", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rindex", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rindex", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "sub", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rindex", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "rjust": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.rjust", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["a", "width", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.rjust", "name": "rjust", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "width", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rjust", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.rjust", "name": "rjust", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "width", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rjust", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["a", "width", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.rjust", "name": "rjust", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "width", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rjust", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.rjust", "name": "rjust", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "width", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rjust", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "width", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rjust", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "width", "<PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rjust", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "rpartition": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.rpartition", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "sep"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.rpartition", "name": "rpartition", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "sep"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rpartition", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.rpartition", "name": "rpartition", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "sep"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rpartition", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "sep"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.rpartition", "name": "rpartition", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "sep"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rpartition", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.rpartition", "name": "rpartition", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "sep"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rpartition", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "sep"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rpartition", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "sep"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rpartition", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "rsplit": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.rsplit", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["a", "sep", "maxsplit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.rsplit", "name": "rsplit", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["a", "sep", "maxsplit"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rsplit", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.rsplit", "name": "rsplit", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["a", "sep", "maxsplit"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rsplit", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["a", "sep", "maxsplit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.rsplit", "name": "rsplit", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["a", "sep", "maxsplit"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rsplit", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.rsplit", "name": "rsplit", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["a", "sep", "maxsplit"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rsplit", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["a", "sep", "maxsplit"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rsplit", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["a", "sep", "maxsplit"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rsplit", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "rstrip": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.rstrip", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "chars"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.rstrip", "name": "rstrip", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "chars"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rstrip", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.rstrip", "name": "rstrip", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "chars"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rstrip", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "chars"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.rstrip", "name": "rstrip", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "chars"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rstrip", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.rstrip", "name": "rstrip", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "chars"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rstrip", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "chars"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rstrip", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "chars"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rstrip", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "split": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.split", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["a", "sep", "maxsplit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.split", "name": "split", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["a", "sep", "maxsplit"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.split", "name": "split", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["a", "sep", "maxsplit"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["a", "sep", "maxsplit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.split", "name": "split", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["a", "sep", "maxsplit"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.split", "name": "split", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["a", "sep", "maxsplit"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["a", "sep", "maxsplit"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["a", "sep", "maxsplit"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "splitlines": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.splitlines", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "keepends"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.splitlines", "name": "splitlines", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "keepends"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "splitlines", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.splitlines", "name": "splitlines", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "keepends"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "splitlines", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "keepends"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.splitlines", "name": "splitlines", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "keepends"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "splitlines", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.splitlines", "name": "splitlines", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "keepends"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "splitlines", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "keepends"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "splitlines", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "keepends"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "splitlines", "ret_type": {".class": "TypeAliasType", "args": ["numpy.object_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "startswith": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.startswith", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "prefix", "start", "end"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.startswith", "name": "startswith", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "prefix", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "startswith", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.startswith", "name": "startswith", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "prefix", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "startswith", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "prefix", "start", "end"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.startswith", "name": "startswith", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "prefix", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "startswith", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.startswith", "name": "startswith", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "prefix", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "startswith", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "prefix", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "startswith", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "prefix", "start", "end"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "startswith", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "str_": {".class": "SymbolTableNode", "cross_ref": "numpy.str_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "str_len": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["A"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.str_len", "name": "str_len", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["A"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str_len", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "strip": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.strip", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "chars"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.strip", "name": "strip", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "chars"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "strip", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.strip", "name": "strip", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "chars"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "strip", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "chars"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.strip", "name": "strip", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "chars"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "strip", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.strip", "name": "strip", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "chars"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "strip", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "chars"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "strip", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "chars"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "strip", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "swapcase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.swapcase", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.swapcase", "name": "swapcase", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "swapcase", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.swapcase", "name": "swapcase", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "swapcase", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.swapcase", "name": "swapcase", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "swapcase", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.swapcase", "name": "swapcase", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "swapcase", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "swapcase", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "swapcase", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "title": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.title", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.title", "name": "title", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "title", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.title", "name": "title", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "title", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.title", "name": "title", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "title", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.title", "name": "title", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "title", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "title", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "title", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "translate": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.translate", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["a", "table", "deletechars"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.translate", "name": "translate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "table", "deletechars"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "translate", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.translate", "name": "translate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "table", "deletechars"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "translate", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["a", "table", "deletechars"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.translate", "name": "translate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "table", "deletechars"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "translate", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.translate", "name": "translate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "table", "deletechars"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "translate", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "table", "deletechars"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "translate", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "table", "deletechars"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "translate", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "upper": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.upper", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.upper", "name": "upper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "upper", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.upper", "name": "upper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "upper", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.upper", "name": "upper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "upper", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.upper", "name": "upper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "upper", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "upper", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "upper", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "zfill": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core.defchararray.zfill", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.zfill", "name": "zfill", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "width"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "zfill", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.zfill", "name": "zfill", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "width"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "zfill", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core.defchararray.zfill", "name": "zfill", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "width"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "zfill", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core.defchararray.zfill", "name": "zfill", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "width"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "zfill", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "width"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeStr_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "zfill", "ret_type": {".class": "TypeAliasType", "args": ["numpy.str_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "width"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBytes_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "zfill", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bytes_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "path": "c:\\AnacondaPath\\Lib\\site-packages\\numpy\\core\\defchararray.pyi"}