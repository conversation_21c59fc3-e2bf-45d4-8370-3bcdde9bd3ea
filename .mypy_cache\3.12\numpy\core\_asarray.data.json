{".class": "MypyFile", "_fullname": "numpy.core._asarray", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DTypeLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like.DTypeLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core._asarray._ArrayType", "name": "_ArrayType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}}, "_E": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy.core._asarray._E", "line": 16, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "E"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ENSUREARRAY"}], "uses_pep604_syntax": false}}}, "_Requirements": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy.core._asarray._Requirements", "line": 9, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "C"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "C_CONTIGUOUS"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "CONTIGUOUS"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "F_CONTIGUOUS"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "FORTRAN"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "A"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ALIGNED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "W"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "WRITEABLE"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "O"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "OWNDATA"}], "uses_pep604_syntax": false}}}, "_RequirementsWithE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy.core._asarray._RequirementsWithE", "line": 17, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.core._asarray._Requirements"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.core._asarray._E"}], "uses_pep604_syntax": false}}}, "_SupportsArrayFunc": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._SupportsArrayFunc", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core._asarray.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core._asarray.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core._asarray.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core._asarray.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core._asarray.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core._asarray.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "ndarray": {".class": "SymbolTableNode", "cross_ref": "numpy.n<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "require": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.core._asarray.require", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5], "arg_names": ["a", "dtype", "requirements", "like"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core._asarray.require", "name": "require", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["a", "dtype", "requirements", "like"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core._asarray._ArrayType", "id": -1, "name": "_ArrayType", "namespace": "numpy.core._asarray.require#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}, {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.core._asarray._Requirements"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.core._asarray._Requirements"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "numpy._typing._array_like._SupportsArrayFunc"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "require", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core._asarray._ArrayType", "id": -1, "name": "_ArrayType", "namespace": "numpy.core._asarray.require#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core._asarray._ArrayType", "id": -1, "name": "_ArrayType", "namespace": "numpy.core._asarray.require#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core._asarray.require", "name": "require", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["a", "dtype", "requirements", "like"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core._asarray._ArrayType", "id": -1, "name": "_ArrayType", "namespace": "numpy.core._asarray.require#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}, {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.core._asarray._Requirements"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.core._asarray._Requirements"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "numpy._typing._array_like._SupportsArrayFunc"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "require", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core._asarray._ArrayType", "id": -1, "name": "_ArrayType", "namespace": "numpy.core._asarray.require#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core._asarray._ArrayType", "id": -1, "name": "_ArrayType", "namespace": "numpy.core._asarray.require#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5], "arg_names": ["a", "dtype", "requirements", "like"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core._asarray.require", "name": "require", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["a", "dtype", "requirements", "like"], "arg_types": ["builtins.object", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.core._asarray._E"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.core._asarray._RequirementsWithE"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "numpy._typing._array_like._SupportsArrayFunc"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "require", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core._asarray.require", "name": "require", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["a", "dtype", "requirements", "like"], "arg_types": ["builtins.object", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.core._asarray._E"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.core._asarray._RequirementsWithE"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "numpy._typing._array_like._SupportsArrayFunc"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "require", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5], "arg_names": ["a", "dtype", "requirements", "like"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.core._asarray.require", "name": "require", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["a", "dtype", "requirements", "like"], "arg_types": ["builtins.object", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.core._asarray._Requirements"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.core._asarray._Requirements"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "numpy._typing._array_like._SupportsArrayFunc"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "require", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.core._asarray.require", "name": "require", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["a", "dtype", "requirements", "like"], "arg_types": ["builtins.object", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.core._asarray._Requirements"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.core._asarray._Requirements"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "numpy._typing._array_like._SupportsArrayFunc"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "require", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["a", "dtype", "requirements", "like"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core._asarray._ArrayType", "id": -1, "name": "_ArrayType", "namespace": "numpy.core._asarray.require#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}, {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.core._asarray._Requirements"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.core._asarray._Requirements"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "numpy._typing._array_like._SupportsArrayFunc"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "require", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core._asarray._ArrayType", "id": -1, "name": "_ArrayType", "namespace": "numpy.core._asarray.require#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.core._asarray._ArrayType", "id": -1, "name": "_ArrayType", "namespace": "numpy.core._asarray.require#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["a", "dtype", "requirements", "like"], "arg_types": ["builtins.object", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.core._asarray._E"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.core._asarray._RequirementsWithE"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "numpy._typing._array_like._SupportsArrayFunc"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "require", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["a", "dtype", "requirements", "like"], "arg_types": ["builtins.object", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.core._asarray._Requirements"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.core._asarray._Requirements"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, "numpy._typing._array_like._SupportsArrayFunc"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "require", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "path": "c:\\AnacondaPath\\Lib\\site-packages\\numpy\\core\\_asarray.pyi"}