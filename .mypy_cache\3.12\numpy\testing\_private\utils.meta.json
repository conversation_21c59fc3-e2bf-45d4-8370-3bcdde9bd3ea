{"data_mtime": 1749245427, "dep_lines": [9, 29, 39, 1, 2, 3, 4, 5, 6, 7, 8, 10, 28, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "numpy._typing", "unittest.case", "os", "sys", "ast", "types", "warnings", "unittest", "contextlib", "re", "typing", "numpy", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nested_sequence"], "hash": "ea11bb71ec31e28d3c7bf8b23daf9ab44a86c68e", "id": "numpy.testing._private.utils", "ignore_all": true, "interface_hash": "cc62255aaafb9be9bfe0f606604b6cf3378737a6", "mtime": 1707226025, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\AnacondaPath\\Lib\\site-packages\\numpy\\testing\\_private\\utils.pyi", "plugin_data": null, "size": 10123, "suppressed": [], "version_id": "1.15.0"}