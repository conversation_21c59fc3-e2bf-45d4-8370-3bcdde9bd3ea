{"data_mtime": 1749245427, "dep_lines": [4, 5, 1, 3, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 30, 30], "dependencies": ["numpy.polynomial._polybase", "numpy.polynomial.polyutils", "typing", "numpy", "builtins", "_frozen_importlib", "abc"], "hash": "3612545efc33c0062f2de348bc1d5dbd74f4ddfb", "id": "numpy.polynomial.laguerre", "ignore_all": true, "interface_hash": "85da6a44e5b63b7e848eb975db3d3a331bdb1672", "mtime": 1707226025, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\AnacondaPath\\Lib\\site-packages\\numpy\\polynomial\\laguerre.pyi", "plugin_data": null, "size": 1178, "suppressed": [], "version_id": "1.15.0"}