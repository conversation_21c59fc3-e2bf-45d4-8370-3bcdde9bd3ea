#!/usr/bin/env python3
"""
Enhanced Data Cleaning Test Script for RS_join_model_MAT2_Regioner_v4

This script tests the Danish data formatting cleaning capabilities
with actual CSV data to verify proper numeric type conversion.

Author: Enhanced for comprehensive Danish format handling
Date: 2024
"""

import pandas as pd
import numpy as np
import re
from typing import Dict, List, Any, Optional


class DanishDataCleaner:
    """Enhanced Danish data format cleaner matching the QGIS script implementation"""
    
    def __init__(self):
        """Initialize with comprehensive field categories"""
        
        # Enhanced field categories matching the QGIS script
        self.numeric_area_fields = [
            "Grundareal", "Registreret areal", "Vejareal", "Fredskovsareal",
            "Strandbeskyttelsesareal", "Brugsretsareal", "Beboelsesareal",
            "Erhvervsareal", "Bygningsareal", "Enhedsareal - Beboelse",
            "Enhedsareal - Erhverv", "Areal af udnyttet del af tagetage",
            "Kælderareal", "Tinglyst areal (Ejerlejligheder)"
        ]
        
        self.price_fields = [
            "Offentlig ejendomsværdi (Ny)", "Offentlig grundværdi (Ny)",
            "Seneste handelspris", "Pris pr. m2 (BR18-§455 etageareal)",
            "Offentlig ejendomsværdi (Historisk)", "Offentlig grundværdi (Historisk)",
            "Offentlig ejendomsværdi (Foreløbig)", "Offentlig grundværdi (Foreløbig)",
            "Grundskyld (Historisk)", "Dækningsafgift (Historisk)",
            "Ejendomsværdiskat (Historisk)", "Totale skat (Historisk)",
            "Grundskyld (Ny)", "Ejendomsværdiskat (Ny)", "Totale skat (Ny)",
            "Grundskyld (Foreløbig)", "Ejendomsværdiskat (Foreløbig)",
            "Totale skat (Foreløbig)", "Pris pr. m2 (samlet enhedsareal)",
            "Pris pr. m2 (enhedsareal)", "Pris pr. m2 (grund)"
        ]
        
        self.percentage_fields = [
            "Realiseret bebyggelsesprocent", "Seneste handlet andel"
        ]
        
        self.integer_fields = [
            "JordstykkeID", "BFE-nummer", "Beboelsesenheder", "Erhvervsenheder",
            "Opførelsesår", "Antal ejere", "Primær ejer alder",
            "Vurderingsår (Historisk)", "Vurderingsår (Ny)", "Vurderingsår (Foreløbig)",
            "Etageareal (BR18-§455)", "Antal værelser", "Omtilbygningsår",
            "Fordelingstal (Tæller)", "Fordelingstal (Nævner)", "Postnr",
            "Primær ejer postnr.", "Kommunekode", "Ejendomsnummer"
        ]

    def clean_area_field(self, value: Any) -> Optional[float]:
        """Clean area fields: remove spaces, replace commas with dots, handle dashes"""
        if pd.isna(value) or value is None:
            return None
            
        str_val = str(value).strip()
        if str_val in ['-', '']:
            return None
            
        # Remove spaces and replace comma with dot
        cleaned = re.sub(r'\s+', '', str_val)
        cleaned = cleaned.replace(',', '.')
        
        try:
            return float(cleaned)
        except ValueError:
            return None

    def clean_price_field(self, value: Any) -> Optional[float]:
        """Clean price fields: remove kr., spaces, commas (thousand separators), handle dashes"""
        if pd.isna(value) or value is None:
            return None
            
        str_val = str(value).strip()
        if str_val in ['-', '- kr.', '']:
            return None
            
        # Remove currency indicators and spaces
        cleaned = re.sub(r'\s*kr\.?\s*$', '', str_val)  # Remove " kr." or " kr" at end
        cleaned = re.sub(r'\s*kr\.?\s*', '', cleaned)   # Remove " kr." anywhere
        cleaned = cleaned.replace(',', '')  # Remove thousand separators
        cleaned = re.sub(r'\s+', '', cleaned)  # Remove all spaces
        
        try:
            return float(cleaned)
        except ValueError:
            return None

    def clean_percentage_field(self, value: Any) -> Optional[float]:
        """Clean percentage fields: remove %, spaces, handle dashes"""
        if pd.isna(value) or value is None:
            return None
            
        str_val = str(value).strip()
        if str_val in ['-', '-%', '']:
            return None
            
        # Remove percentage sign
        cleaned = str_val.replace('%', '')
        
        try:
            return float(cleaned)
        except ValueError:
            return None

    def clean_integer_field(self, value: Any) -> Optional[int]:
        """Clean integer fields: handle dashes, spaces, boolean-like strings"""
        if pd.isna(value) or value is None:
            return None
            
        str_val = str(value).strip()
        if str_val in ['-', '']:
            return None
            
        # Handle boolean-like values
        lower_val = str_val.lower()
        if lower_val in ['true', 'ja', 'yes', 'sand', '1']:
            return 1
        elif lower_val in ['false', 'nej', 'no', 'falsk', '0']:
            return 0
            
        # Remove spaces and try to convert
        cleaned = re.sub(r'\s+', '', str_val)
        
        try:
            return int(float(cleaned))  # Convert via float to handle decimals
        except ValueError:
            return None

    def clean_string_field(self, value: Any) -> Optional[str]:
        """Clean string fields: handle dashes and whitespace"""
        if pd.isna(value) or value is None:
            return None
            
        str_val = str(value).strip()
        if str_val == '-':
            return None
            
        return str_val if str_val else None

    def clean_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean entire dataframe using field-specific cleaning methods"""
        
        cleaned_df = df.copy()
        
        for column in cleaned_df.columns:
            print(f"Cleaning column: {column}")
            
            if column in self.numeric_area_fields:
                cleaned_df[column] = cleaned_df[column].apply(self.clean_area_field)
                print(f"  → Cleaned as area field (numeric)")
                
            elif column in self.price_fields:
                cleaned_df[column] = cleaned_df[column].apply(self.clean_price_field)
                print(f"  → Cleaned as price field (numeric)")
                
            elif column in self.percentage_fields:
                cleaned_df[column] = cleaned_df[column].apply(self.clean_percentage_field)
                print(f"  → Cleaned as percentage field (numeric)")
                
            elif column in self.integer_fields:
                cleaned_df[column] = cleaned_df[column].apply(self.clean_integer_field)
                print(f"  → Cleaned as integer field")
                
            else:
                cleaned_df[column] = cleaned_df[column].apply(self.clean_string_field)
                print(f"  → Cleaned as string field")
        
        return cleaned_df

    def analyze_cleaning_results(self, original_df: pd.DataFrame, cleaned_df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze the results of data cleaning"""
        
        results = {
            'total_columns': len(original_df.columns),
            'total_rows': len(original_df),
            'field_analysis': {},
            'summary': {}
        }
        
        for column in original_df.columns:
            original_nulls = original_df[column].isna().sum()
            cleaned_nulls = cleaned_df[column].isna().sum()
            
            # Count dash conversions
            dash_count = (original_df[column].astype(str).str.strip() == '-').sum()
            
            field_info = {
                'original_nulls': int(original_nulls),
                'cleaned_nulls': int(cleaned_nulls),
                'dash_conversions': int(dash_count),
                'original_dtype': str(original_df[column].dtype),
                'cleaned_dtype': str(cleaned_df[column].dtype),
                'field_category': self._get_field_category(column)
            }
            
            # Sample values for verification
            non_null_original = original_df[column].dropna()
            non_null_cleaned = cleaned_df[column].dropna()
            
            if len(non_null_original) > 0:
                field_info['sample_original'] = str(non_null_original.iloc[0])
            if len(non_null_cleaned) > 0:
                field_info['sample_cleaned'] = str(non_null_cleaned.iloc[0])
                
            results['field_analysis'][column] = field_info
        
        # Summary statistics
        results['summary'] = {
            'total_dash_conversions': sum(info['dash_conversions'] for info in results['field_analysis'].values()),
            'numeric_fields_converted': len([col for col, info in results['field_analysis'].items() 
                                           if info['field_category'] in ['area', 'price', 'percentage', 'integer']]),
            'string_fields': len([col for col, info in results['field_analysis'].items() 
                                if info['field_category'] == 'string'])
        }
        
        return results

    def _get_field_category(self, column: str) -> str:
        """Get the category of a field"""
        if column in self.numeric_area_fields:
            return 'area'
        elif column in self.price_fields:
            return 'price'
        elif column in self.percentage_fields:
            return 'percentage'
        elif column in self.integer_fields:
            return 'integer'
        else:
            return 'string'


def test_csv_cleaning(csv_file: str) -> None:
    """Test data cleaning on a CSV file"""
    
    print(f"\n{'='*60}")
    print(f"TESTING DATA CLEANING: {csv_file}")
    print(f"{'='*60}")
    
    try:
        # Load CSV with various delimiters
        for delimiter in [',', ';', '\t']:
            try:
                df = pd.read_csv(csv_file, delimiter=delimiter, encoding='utf-8')
                if len(df.columns) > 1:  # Found the right delimiter
                    break
            except:
                continue
        else:
            # Try with different encoding
            df = pd.read_csv(csv_file, delimiter=';', encoding='latin-1')
        
        print(f"✅ Loaded CSV: {len(df)} rows, {len(df.columns)} columns")
        print(f"Columns: {list(df.columns[:5])}{'...' if len(df.columns) > 5 else ''}")
        
        # Initialize cleaner and clean data
        cleaner = DanishDataCleaner()
        cleaned_df = cleaner.clean_dataframe(df)
        
        # Analyze results
        analysis = cleaner.analyze_cleaning_results(df, cleaned_df)
        
        print(f"\n📊 CLEANING ANALYSIS:")
        print(f"  Total fields processed: {analysis['total_columns']}")
        print(f"  Numeric fields converted: {analysis['summary']['numeric_fields_converted']}")
        print(f"  String fields: {analysis['summary']['string_fields']}")
        print(f"  Total dash→NULL conversions: {analysis['summary']['total_dash_conversions']}")
        
        # Show detailed results for key fields
        key_fields = ['Grundareal', 'Seneste handelspris', 'Realiseret bebyggelsesprocent', 'BFE-nummer']
        
        print(f"\n🔍 KEY FIELD ANALYSIS:")
        for field in key_fields:
            if field in analysis['field_analysis']:
                info = analysis['field_analysis'][field]
                print(f"  {field} ({info['field_category']}):")
                print(f"    Original dtype: {info['original_dtype']} → Cleaned dtype: {info['cleaned_dtype']}")
                print(f"    NULL conversions: {info['original_nulls']} → {info['cleaned_nulls']} (+{info['dash_conversions']} from dashes)")
                if 'sample_original' in info and 'sample_cleaned' in info:
                    print(f"    Sample: '{info['sample_original']}' → {info['sample_cleaned']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error processing {csv_file}: {str(e)}")
        return False


def main():
    """Main test function"""
    
    print("🧪 ENHANCED DANISH DATA CLEANING TEST")
    print("Testing data cleaning capabilities matching RS_join_model_MAT2_Regioner_v4")
    
    # Test files
    test_files = [
        'Stamdata_RS.csv',
        'Jordstykker_RS.csv',
        'jordstykke.csv'
    ]
    
    success_count = 0
    
    for csv_file in test_files:
        if test_csv_cleaning(csv_file):
            success_count += 1
    
    print(f"\n{'='*60}")
    print(f"TEST SUMMARY: {success_count}/{len(test_files)} files processed successfully")
    print(f"{'='*60}")
    
    if success_count == len(test_files):
        print("✅ All tests passed! Data cleaning is working correctly.")
        print("The enhanced script should handle Danish formatting properly.")
    else:
        print("⚠️  Some tests failed. Check the error messages above.")


if __name__ == "__main__":
    main()
