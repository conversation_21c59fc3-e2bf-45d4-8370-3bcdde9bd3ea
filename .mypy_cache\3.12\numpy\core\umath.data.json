{".class": "MypyFile", "_fullname": "numpy.core.umath", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "_UFUNC_API": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "numpy.core.umath._UFUNC_API", "name": "_UFUNC_API", "type": {".class": "AnyType", "missing_import_name": "numpy.core.umath._UFUNC_API", "source_any": null, "type_of_any": 3}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.core.umath.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.umath.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.umath.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.umath.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.umath.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.umath.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.umath.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_add_newdoc_ufunc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "numpy.core.umath._add_newdoc_ufunc", "name": "_add_newdoc_ufunc", "type": {".class": "AnyType", "missing_import_name": "numpy.core.umath._add_newdoc_ufunc", "source_any": null, "type_of_any": 3}}}, "_multiarray_umath": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "numpy.core.umath._multiarray_umath", "name": "_multiarray_umath", "type": {".class": "AnyType", "missing_import_name": "numpy.core.umath._multiarray_umath", "source_any": null, "type_of_any": 3}}}, "_ones_like": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "numpy.core.umath._ones_like", "name": "_ones_like", "type": {".class": "AnyType", "missing_import_name": "numpy.core.umath._ones_like", "source_any": null, "type_of_any": 3}}}}, "path": "c:\\AnacondaPath\\Lib\\site-packages\\numpy\\core\\umath.py"}