{"data_mtime": 1749245426, "dep_lines": [38, 39, 46, 47, 45, 11, 13, 21, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["numpy._typing._nbit", "numpy._typing._scalars", "numpy._typing._array_like", "numpy._typing._nested_sequence", "numpy._typing", "__future__", "typing", "numpy", "builtins", "_frozen_importlib", "abc", "types"], "hash": "d2828d90c915ecfcf15aa3e9d10a2d068ab9f706", "id": "numpy._typing._callable", "ignore_all": true, "interface_hash": "a72c0c2c130294062dad3364d66a09c84817d453", "mtime": 1707226024, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\AnacondaPath\\Lib\\site-packages\\numpy\\_typing\\_callable.pyi", "plugin_data": null, "size": 11121, "suppressed": [], "version_id": "1.15.0"}