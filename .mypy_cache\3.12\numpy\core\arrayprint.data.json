{".class": "MypyFile", "_fullname": "numpy.core.arrayprint", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SupportsIndex": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsIndex", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CharLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._CharLike_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FloatLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._FloatLike_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FloatMode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy.core.arrayprint._FloatMode", "line": 26, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "fixed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "unique"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "maxprec"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "maxprec_equal"}], "uses_pep604_syntax": false}}}, "_FormatDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.core.arrayprint._FormatDict", "name": "_FormatDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.core.arrayprint._FormatDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.core.arrayprint", "mro": ["numpy.core.arrayprint._FormatDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["bool", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["numpy.bool_"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["int", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["<PERSON><PERSON><PERSON>", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["numpy.timedelta64"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["datetime", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["numpy.datetime64"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["float", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["longfloat", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.longdouble"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["complexfloat", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["longcomplexfloat", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.clongdouble"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["void", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["numpy.void"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["numpystr", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._CharLike_co"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["object", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["all", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["int_kind", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["float_kind", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["complex_kind", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["str_kind", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._CharLike_co"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]], "readonly_keys": [], "required_keys": []}}}, "_FormatOptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.core.arrayprint._FormatOptions", "name": "_FormatOptions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.core.arrayprint._FormatOptions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.core.arrayprint", "mro": ["numpy.core.arrayprint._FormatOptions", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["precision", "builtins.int"], ["threshold", "builtins.int"], ["edgeitems", "builtins.int"], ["linewidth", "builtins.int"], ["suppress", "builtins.bool"], ["nanstr", "builtins.str"], ["infstr", "builtins.str"], ["formatter", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.core.arrayprint._FormatDict"}], "uses_pep604_syntax": true}], ["sign", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "-"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "+"}, {".class": "LiteralType", "fallback": "builtins.str", "value": " "}], "uses_pep604_syntax": false}], ["floatmode", {".class": "TypeAliasType", "args": [], "type_ref": "numpy.core.arrayprint._FloatMode"}], ["legacy", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1.13"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1.21"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": ["edgeitems", "floatmode", "formatter", "infstr", "legacy", "linewidth", "nanstr", "precision", "sign", "suppress", "threshold"]}}}, "_GeneratorContextManager": {".class": "SymbolTableNode", "cross_ref": "contextlib._GeneratorContextManager", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.arrayprint.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.arrayprint.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.arrayprint.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.arrayprint.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.arrayprint.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.core.arrayprint.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "array2string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["a", "max_line_width", "precision", "suppress_small", "separator", "prefix", "formatter", "threshold", "edgeitems", "sign", "floatmode", "suffix", "legacy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.arrayprint.array2string", "name": "array2string", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["a", "max_line_width", "precision", "suppress_small", "separator", "prefix", "formatter", "threshold", "edgeitems", "sign", "floatmode", "suffix", "legacy"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.bool"], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.core.arrayprint._FormatDict"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "-"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "+"}, {".class": "LiteralType", "fallback": "builtins.str", "value": " "}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.core.arrayprint._FloatMode"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1.13"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1.21"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array2string", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "array_repr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["arr", "max_line_width", "precision", "suppress_small"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.arrayprint.array_repr", "name": "array_repr", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["arr", "max_line_width", "precision", "suppress_small"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.bool"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array_repr", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "array_str": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "max_line_width", "precision", "suppress_small"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.arrayprint.array_str", "name": "array_str", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "max_line_width", "precision", "suppress_small"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.bool"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "array_str", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bool_": {".class": "SymbolTableNode", "cross_ref": "numpy.bool_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "bytes_": {".class": "SymbolTableNode", "cross_ref": "numpy.bytes_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "clongdouble": {".class": "SymbolTableNode", "cross_ref": "numpy.clongdouble", "kind": "Gdef", "module_hidden": true, "module_public": false}, "complexfloating": {".class": "SymbolTableNode", "cross_ref": "numpy.complexfloating", "kind": "Gdef", "module_hidden": true, "module_public": false}, "datetime64": {".class": "SymbolTableNode", "cross_ref": "numpy.datetime64", "kind": "Gdef", "module_hidden": true, "module_public": false}, "floating": {".class": "SymbolTableNode", "cross_ref": "numpy.floating", "kind": "Gdef", "module_hidden": true, "module_public": false}, "format_float_positional": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["x", "precision", "unique", "fractional", "trim", "sign", "pad_left", "pad_right", "min_digits"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.arrayprint.format_float_positional", "name": "format_float_positional", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["x", "precision", "unique", "fractional", "trim", "sign", "pad_left", "pad_right", "min_digits"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "k"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "."}, {".class": "LiteralType", "fallback": "builtins.str", "value": "0"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "-"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_float_positional", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "format_float_scientific": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["x", "precision", "unique", "trim", "sign", "pad_left", "exp_digits", "min_digits"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.arrayprint.format_float_scientific", "name": "format_float_scientific", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["x", "precision", "unique", "trim", "sign", "pad_left", "exp_digits", "min_digits"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "k"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "."}, {".class": "LiteralType", "fallback": "builtins.str", "value": "0"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "-"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_float_scientific", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generic": {".class": "SymbolTableNode", "cross_ref": "numpy.generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "get_printoptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.arrayprint.get_printoptions", "name": "get_printoptions", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_printoptions", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.core.arrayprint._FormatOptions"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "integer": {".class": "SymbolTableNode", "cross_ref": "numpy.integer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "longdouble": {".class": "SymbolTableNode", "cross_ref": "numpy.longdouble", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ndarray": {".class": "SymbolTableNode", "cross_ref": "numpy.n<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "printoptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5], "arg_names": ["precision", "threshold", "edgeitems", "linewidth", "suppress", "nanstr", "infstr", "formatter", "sign", "floatmode", "legacy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.arrayprint.printoptions", "name": "printoptions", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5], "arg_names": ["precision", "threshold", "edgeitems", "linewidth", "suppress", "nanstr", "infstr", "formatter", "sign", "floatmode", "legacy"], "arg_types": [{".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.bool"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.core.arrayprint._FormatDict"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "-"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "+"}, {".class": "LiteralType", "fallback": "builtins.str", "value": " "}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.core.arrayprint._FloatMode"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1.13"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1.21"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "printoptions", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.core.arrayprint._FormatOptions"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_printoptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5], "arg_names": ["precision", "threshold", "edgeitems", "linewidth", "suppress", "nanstr", "infstr", "formatter", "sign", "floatmode", "legacy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.arrayprint.set_printoptions", "name": "set_printoptions", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5], "arg_names": ["precision", "threshold", "edgeitems", "linewidth", "suppress", "nanstr", "infstr", "formatter", "sign", "floatmode", "legacy"], "arg_types": [{".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.bool"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.core.arrayprint._FormatDict"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "-"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "+"}, {".class": "LiteralType", "fallback": "builtins.str", "value": " "}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.core.arrayprint._FloatMode"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1.13"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "1.21"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_printoptions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_string_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["f", "repr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.core.arrayprint.set_string_function", "name": "set_string_function", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["f", "repr"], "arg_types": [{".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_string_function", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "str_": {".class": "SymbolTableNode", "cross_ref": "numpy.str_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "timedelta64": {".class": "SymbolTableNode", "cross_ref": "numpy.timedelta64", "kind": "Gdef", "module_hidden": true, "module_public": false}, "void": {".class": "SymbolTableNode", "cross_ref": "numpy.void", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\AnacondaPath\\Lib\\site-packages\\numpy\\core\\arrayprint.pyi"}