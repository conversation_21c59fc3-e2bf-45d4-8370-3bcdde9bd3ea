"""
Enhanced RS_join_model_MAT2_Regional - PyQGIS Best Practices Version
Implements regional processing with proper resource management and error handling
Fixed: GeoPackage output path handling and improved temp file cleanup
Fixed: Automatically removes 'layer' and 'path' artifact fields from final output
Enhanced: Added comprehensive building and property attributes for analysis
Enhanced: Added calculated Areal_ha field
Enhanced: Logical field ordering for optimal data analysis workflow
Enhanced: Complete land-related attributes including protection areas and coordinates
"""

import os
import sys
import tempfile
import shutil
import time
from datetime import datetime
import gc
from typing import Dict, List, Optional, Any, Union

from qgis.core import (
    QgsProcessing,
    QgsProcessingAlgorithm,
    QgsProcessingMultiStepFeedback,
    QgsProcessingParameterVectorLayer,
    QgsProcessingParameterFeatureSink,
    QgsProcessingParameterBoolean,
    QgsProcessingParameterNumber,
    QgsProcessingParameterString,
    QgsProcessingParameterFile,
    QgsProcessingParameterEnum,
    QgsProcessingException,
    QgsVectorLayer,
    QgsProject,
    QgsMessageLog,
    Qgis,
    QgsProcessingContext,
    QgsCoordinateReferenceSystem,
    QgsWkbTypes,
    QgsFeature,
    QgsProcessingUtils,
    QgsProcessingOutputVectorLayer,
    QgsField,
    QgsFields,
    QgsProcessingParameterField,
)
from qgis.PyQt.QtCore import QVariant
import processing


class EnhancedRsJoinModelMat2Regional(QgsProcessingAlgorithm):
    """
    Enhanced regional processing algorithm that exactly replicates MAT2 workflow
    for each region, with improved NULL handling and temp file management.

    Key features:
    - Accepts CSV files for stamdata_rs and jordstykker_rs (subset filtering)
    - Processes large jordstykke datasets by region for memory efficiency
    - Exact MAT2 7-step workflow compliance per region
    - Proper GeoPackage output handling with layer name support
    - Enhanced temporary file cleanup with Windows compatibility
    - Automatically removes 'layer' and 'path' processing artifact fields
    - Includes comprehensive building and property attributes
    - Calculates Areal_ha field for standardized area measurement
    - Complete land attributes including protection areas and spatial coordinates
    """

    # Algorithm parameters
    INPUT_JORDSTYKKE = "jordstykke_udtrk_senestesagloaklid"
    INPUT_STAMDATA = "stamdata_rs"
    INPUT_JORDSTYKKER = "jordstykker_rs"
    ENABLE_REGIONAL = "enable_regional_split"
    REGION_FIELD = "region_field"
    REGION_CODES = "region_codes"
    TEMP_DIRECTORY = "temp_directory"
    CLEANUP_TEMP = "cleanup_temp_files"
    OUTPUT = "Lodsejerinformation_rs"

    # Field mappings as class constants for maintainability
    JORDSTYKKER_FIELD_MAP = [
        {
            "expression": '"JordstykkeID"',
            "length": 0,
            "name": "JordstykkeID",
            "precision": 0,
            "type": QVariant.Int,
        },
        {
            "expression": '"Matrikelnr"',
            "length": 0,
            "name": "Matrikelnr",
            "precision": 0,
            "type": QVariant.String,
        },
        {
            "expression": '"Ejerlavsnavn"',
            "length": 0,
            "name": "Ejerlavsnavn",
            "precision": 0,
            "type": QVariant.String,
        },
        # New land-related attributes from Jordstykker_RS
        {
            "expression": '"Vejareal"',
            "length": 0,
            "name": "Vejareal",
            "precision": 0,
            "type": QVariant.String,
        },
        {
            "expression": '"Fredskovsareal"',
            "length": 0,
            "name": "Fredskovsareal",
            "precision": 0,
            "type": QVariant.String,
        },
        {
            "expression": '"Strandbeskyttelsesareal"',
            "length": 0,
            "name": "Strandbeskyttelsesareal",
            "precision": 0,
            "type": QVariant.String,
        },
        {
            "expression": '"Fredskovsareal omfang"',
            "length": 0,
            "name": "Fredskovsareal omfang",
            "precision": 0,
            "type": QVariant.String,
        },
        {
            "expression": '"Strandbeskyttelse omfang"',
            "length": 0,
            "name": "Strandbeskyttelse omfang",
            "precision": 0,
            "type": QVariant.String,
        },
        {
            "expression": '"Er fælleslod"',
            "length": 0,
            "name": "Er fælleslod",
            "precision": 0,
            "type": QVariant.String,
        },
        {
            "expression": '"Longitude"',
            "length": 0,
            "name": "Longitude",
            "precision": 6,
            "type": QVariant.Double,
        },
        {
            "expression": '"Latitude"',
            "length": 0,
            "name": "Latitude",
            "precision": 6,
            "type": QVariant.Double,
        },
    ]

    STAMDATA_FIELD_MAP = [
        # Original ownership fields
        {
            "expression": '"BFE-nummer"',
            "length": 0,
            "name": "BFE-nummer",
            "precision": 0,
            "type": QVariant.Int,
        },
        {
            "expression": '"Antal ejere"',
            "length": 0,
            "name": "Antal ejere",
            "precision": 0,
            "type": QVariant.Int,
        },
        {
            "expression": '"Primær ejer"',
            "length": 0,
            "name": "Primær ejer",
            "precision": 0,
            "type": QVariant.String,
        },
        {
            "expression": '"Primær ejer alder"',
            "length": 0,
            "name": "Primær ejer alder",
            "precision": 0,
            "type": QVariant.String,
        },
        {
            "expression": '"Postlinje 1"',
            "length": 0,
            "name": "Postlinje 1",
            "precision": 0,
            "type": QVariant.String,
        },
        {
            "expression": '"Postnr"',
            "length": 0,
            "name": "Postnr",
            "precision": 0,
            "type": QVariant.String,
        },
        {
            "expression": '"By"',
            "length": 0,
            "name": "By",
            "precision": 0,
            "type": QVariant.String,
        },
        # New building/property attributes
        {
            "expression": '"Seneste handelspris"',
            "length": 0,
            "name": "Seneste handelspris",
            "precision": 0,
            "type": QVariant.String,
        },
        {
            "expression": '"Seneste handelsdato"',
            "length": 0,
            "name": "Seneste handelsdato",
            "precision": 0,
            "type": QVariant.String,
        },
        {
            "expression": '"Anvendelse"',
            "length": 0,
            "name": "Anvendelse",
            "precision": 0,
            "type": QVariant.String,
        },
        {
            "expression": '"Opførelsesår"',
            "length": 0,
            "name": "Opførelsesår",
            "precision": 0,
            "type": QVariant.String,
        },
        {
            "expression": '"Type"',
            "length": 0,
            "name": "Type",
            "precision": 0,
            "type": QVariant.String,
        },
        {
            "expression": '"Undertype"',
            "length": 0,
            "name": "Undertype",
            "precision": 0,
            "type": QVariant.String,
        },
        {
            "expression": '"Energimærke"',
            "length": 0,
            "name": "Energimærke",
            "precision": 0,
            "type": QVariant.String,
        },
        {
            "expression": '"Offentlig ejendomsværdi (Ny)"',
            "length": 0,
            "name": "Offentlig ejendomsværdi (Ny)",
            "precision": 0,
            "type": QVariant.String,
        },
        {
            "expression": '"Realiseret bebyggelsesprocent"',
            "length": 0,
            "name": "Realiseret bebyggelsesprocent",
            "precision": 0,
            "type": QVariant.String,
        },
        # New land-related field from Stamdata_RS
        {
            "expression": '"Grundareal"',
            "length": 0,
            "name": "Grundareal",
            "precision": 0,
            "type": QVariant.String,
        },
    ]

    def __init__(self):
        super().__init__()
        self.feedback: Optional[QgsProcessingMultiStepFeedback] = None
        self.context: Optional[QgsProcessingContext] = None
        self.temp_layers: List[QgsVectorLayer] = []
        self.temp_files: List[str] = []
        self.temp_workspace: Optional[str] = None

    def initAlgorithm(self, config: Optional[Dict[str, Any]] = None) -> None:
        """Initialize algorithm parameters with proper types and validation"""

        # Input layers - Jordstykke polygon layer
        self.addParameter(
            QgsProcessingParameterVectorLayer(
                self.INPUT_JORDSTYKKE,
                "Jordstykke udtrækssag med lokale id",
                types=[QgsProcessing.TypeVectorPolygon],
                defaultValue=None,
            )
        )

        # CSV file inputs - these are 459-record subsets for filtering
        self.addParameter(
            QgsProcessingParameterFile(
                self.INPUT_STAMDATA,
                "Stamdata_RS (CSV file)",
                behavior=QgsProcessingParameterFile.File,
                fileFilter="CSV files (*.csv);;All files (*.*)",
                defaultValue=None,
            )
        )

        self.addParameter(
            QgsProcessingParameterFile(
                self.INPUT_JORDSTYKKER,
                "Jordstykker_RS (CSV file)",
                behavior=QgsProcessingParameterFile.File,
                fileFilter="CSV files (*.csv);;All files (*.*)",
                defaultValue=None,
            )
        )

        # Regional processing parameters
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.ENABLE_REGIONAL,
                "Enable Regional Split Processing",
                defaultValue=True,
            )
        )

        # Use field parameter instead of index for better usability
        self.addParameter(
            QgsProcessingParameterField(
                self.REGION_FIELD,
                "Region Field",
                parentLayerParameterName=self.INPUT_JORDSTYKKE,
                type=QgsProcessingParameterField.Any,
                defaultValue="regionskode",
                optional=True,
            )
        )

        self.addParameter(
            QgsProcessingParameterString(
                self.REGION_CODES,
                "Region Codes (comma-separated)",
                defaultValue="1081,1082,1083,1084,1085",
                optional=True,
            )
        )

        # Enhanced temp management
        self.addParameter(
            QgsProcessingParameterFile(
                self.TEMP_DIRECTORY,
                "Temporary Directory (optional)",
                behavior=QgsProcessingParameterFile.Folder,
                optional=True,
                defaultValue=None,
            )
        )

        self.addParameter(
            QgsProcessingParameterBoolean(
                self.CLEANUP_TEMP, "Cleanup Temporary Files", defaultValue=True
            )
        )

        # Output parameter
        self.addParameter(
            QgsProcessingParameterFeatureSink(
                self.OUTPUT,
                "Lodsejerinformation_RS",
                type=QgsProcessing.TypeVectorAnyGeometry,
                createByDefault=True,
                supportsAppend=True,
                defaultValue=None,
            )
        )

    def processAlgorithm(
        self,
        parameters: Dict[str, Any],
        context: QgsProcessingContext,
        feedback: QgsProcessingMultiStepFeedback,
    ) -> Dict[str, Any]:
        """Main processing algorithm with enhanced error handling"""

        # Initialize instance variables
        self.feedback = feedback
        self.context = context
        self.temp_layers = []
        self.temp_files = []

        try:
            # Validate inputs
            self._validate_inputs(parameters)

            # Setup temporary workspace
            self._setup_temp_workspace(parameters)

            # Process based on regional split setting
            if parameters[self.ENABLE_REGIONAL]:
                return self._process_with_regional_split(parameters)
            else:
                return self._process_standard_mat2(parameters)

        except QgsProcessingException as e:
            # Re-raise processing exceptions
            raise e
        except Exception as e:
            # Wrap other exceptions
            raise QgsProcessingException(f"Processing failed: {str(e)}") from e
        finally:
            # Always cleanup if requested
            if parameters.get(self.CLEANUP_TEMP, True):
                self._cleanup_resources()

    def _validate_inputs(self, parameters: Dict[str, Any]) -> None:
        """Validate input parameters before processing"""

        # Check jordstykke layer
        jordstykke_layer = self.parameterAsVectorLayer(
            parameters, self.INPUT_JORDSTYKKE, self.context
        )
        if not jordstykke_layer or not jordstykke_layer.isValid():
            raise QgsProcessingException("Invalid jordstykke layer")

        # Check CSV files exist
        stamdata_path = self.parameterAsFile(
            parameters, self.INPUT_STAMDATA, self.context
        )
        if not stamdata_path or not os.path.exists(stamdata_path):
            raise QgsProcessingException(
                f"Stamdata CSV file not found: {stamdata_path}"
            )

        jordstykker_path = self.parameterAsFile(
            parameters, self.INPUT_JORDSTYKKER, self.context
        )
        if not jordstykker_path or not os.path.exists(jordstykker_path):
            raise QgsProcessingException(
                f"Jordstykker CSV file not found: {jordstykker_path}"
            )

        # Validate region field if regional processing enabled
        if parameters.get(self.ENABLE_REGIONAL):
            region_field = parameters.get(self.REGION_FIELD)

            if region_field and region_field not in jordstykke_layer.fields().names():
                raise QgsProcessingException(
                    f"Region field '{region_field}' not found in jordstykke layer"
                )

    def _load_csv_as_layer(self, csv_path: str, layer_name: str) -> QgsVectorLayer:
        """Load CSV file as vector layer with proper field detection"""

        self._log_message(f"Loading CSV: {os.path.basename(csv_path)}")

        # Normalize path for Windows
        csv_path = os.path.normpath(csv_path).replace("\\", "/")

        # Build URI for CSV layer with proper options
        # Try comma delimiter first (most common)
        uri = (
            f"file:///{csv_path}?"
            f"type=csv&"
            f"delimiter=,&"
            f"detectTypes=yes&"
            f"geomType=none&"
            f"subsetIndex=no&"
            f"watchFile=no&"
            f"encoding=UTF-8"
        )

        # Create layer
        layer = QgsVectorLayer(uri, layer_name, "delimitedtext")

        if not layer.isValid() or layer.featureCount() == 0:
            # Try with semicolon delimiter
            self._log_message("  Trying semicolon delimiter...")
            uri = uri.replace("delimiter=,", "delimiter=;")
            layer = QgsVectorLayer(uri, layer_name, "delimitedtext")

        if not layer.isValid() or layer.featureCount() == 0:
            # Try with tab delimiter
            self._log_message("  Trying tab delimiter...")
            uri = uri.replace("delimiter=;", "delimiter=\\t")
            layer = QgsVectorLayer(uri, layer_name, "delimitedtext")

        if not layer.isValid():
            raise QgsProcessingException(
                f"Cannot load CSV file: {csv_path}\n"
                f"Please check the file exists and has valid CSV format."
            )

        if layer.featureCount() == 0:
            raise QgsProcessingException(f"CSV file appears to be empty: {csv_path}")

        # Log field information for debugging
        field_names = [field.name() for field in layer.fields()]
        self._log_message(f"  ✓ Fields detected: {', '.join(field_names)}")
        self._log_message(f"  ✓ Record count: {layer.featureCount()}")

        # Validate expected fields based on layer name with enhanced checks
        if layer_name == "stamdata_rs":
            required_fields = [
                "BFE-nummer",
                "Seneste handelspris",
                "Anvendelse",
                "Grundareal",
            ]
            missing = [f for f in required_fields if f not in field_names]
            if missing:
                self._log_message(
                    f"  ⚠️ Warning: Expected field(s) missing: {', '.join(missing)}"
                )

        elif layer_name == "jordstykker_rs":
            required_fields = [
                "JordstykkeID",
                "Matrikelnr",
                "Vejareal",
                "Fredskovsareal",
                "Longitude",
                "Latitude",
            ]
            missing = [f for f in required_fields if f not in field_names]
            if missing:
                self._log_message(
                    f"  ⚠️ Warning: Expected field(s) missing: {', '.join(missing)}"
                )

        # Track for cleanup
        self.temp_layers.append(layer)

        return layer

    def _setup_temp_workspace(self, parameters: Dict[str, Any]) -> None:
        """Setup temporary workspace with proper error handling"""

        self._log_message("=" * 60)
        self._log_message(
            "ENHANCED REGIONAL SPLIT PROCESSING WITH COMPLETE LAND ATTRIBUTES"
        )
        self._log_message("=" * 60)

        # Get temp directory from parameters or use system default
        temp_base = self.parameterAsFile(parameters, self.TEMP_DIRECTORY, self.context)

        # If user specified a temp directory, use it
        if temp_base and os.path.exists(temp_base):
            if not os.access(temp_base, os.W_OK):
                self._log_message(
                    f"⚠️ No write access to {temp_base}, using system temp"
                )
                temp_base = None

        # Create session directory if we have a base directory
        if temp_base:
            # Create unique session directory
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.temp_workspace = os.path.join(
                temp_base, f"regional_processing_{timestamp}"
            )

            try:
                os.makedirs(self.temp_workspace, exist_ok=True)
                self._log_message(f"✅ Temporary workspace: {self.temp_workspace}")
            except Exception as e:
                self._log_message(f"⚠️ Cannot create temp workspace: {str(e)}")
                self._log_message("   Using QGIS temp file management instead")
                self.temp_workspace = None
        else:
            # No specific temp directory, use QGIS temp management
            self.temp_workspace = None
            self._log_message("✅ Using QGIS temporary file management")

    def _process_with_regional_split(
        self, parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process with regional split using best practices"""

        # Parse region codes
        region_codes_str = self.parameterAsString(
            parameters, self.REGION_CODES, self.context
        )
        region_codes = [
            code.strip() for code in region_codes_str.split(",") if code.strip()
        ]

        if not region_codes:
            raise QgsProcessingException("No region codes specified")

        # Get jordstykke layer
        jordstykke_layer = self.parameterAsVectorLayer(
            parameters, self.INPUT_JORDSTYKKE, self.context
        )

        # Load CSV files as layers
        stamdata_path = self.parameterAsFile(
            parameters, self.INPUT_STAMDATA, self.context
        )
        jordstykker_path = self.parameterAsFile(
            parameters, self.INPUT_JORDSTYKKER, self.context
        )

        self._log_message("\n📁 Loading CSV data files...")
        stamdata_layer = self._load_csv_as_layer(stamdata_path, "stamdata_rs")
        jordstykker_layer = self._load_csv_as_layer(jordstykker_path, "jordstykker_rs")

        # Get region field
        region_field = parameters.get(self.REGION_FIELD, "regionskode")

        self._log_message(f"\nRegion field: {region_field}")
        self._log_message(
            f"Regions to process: {', '.join(region_codes)}"
        )  # Setup multi-step feedback
        total_steps = (
            len(region_codes) * 8 + 1
        )  # 8 steps per region + merge (added cleaning step)
        multi_feedback = QgsProcessingMultiStepFeedback(total_steps, self.feedback)
        current_step = 0

        # Process each region
        regional_outputs = []

        for region_code in region_codes:
            if multi_feedback.isCanceled():
                break

            self._log_message(f"\n📍 Processing region {region_code}")

            try:
                # Extract region with proper feedback
                region_layer = self._extract_region(
                    jordstykke_layer, region_field, region_code, multi_feedback
                )

                if not region_layer:
                    self._log_message(f"⚠️ No features found for region {region_code}")
                    continue

                # Run MAT2 workflow for region
                region_output = self._run_mat2_workflow_for_region(
                    region_layer,
                    stamdata_layer,
                    jordstykker_layer,
                    region_code,
                    multi_feedback,
                    current_step,
                )

                if region_output:
                    regional_outputs.append(region_output)
                    self._log_message(f"✅ Region {region_code} completed")

                # Update progress
                current_step += 7

            except Exception as e:
                self._log_message(f"❌ Error processing region {region_code}: {str(e)}")
                continue

        # Check if we have outputs to merge
        if not regional_outputs:
            raise QgsProcessingException("No regional outputs to merge")

        # Merge results
        return self._merge_regional_outputs(
            regional_outputs, parameters, multi_feedback
        )

    def _extract_region(
        self,
        source_layer: QgsVectorLayer,
        region_field: str,
        region_code: str,
        feedback: QgsProcessingMultiStepFeedback,
    ) -> Optional[QgsVectorLayer]:
        """Extract features for specific region with validation"""

        self._log_message(f"Extracting features for region {region_code}...")

        # Use memory layer for better performance
        extract_params = {
            "INPUT": source_layer,
            "FIELD": region_field,
            "OPERATOR": 0,  # equals
            "VALUE": region_code,
            "OUTPUT": "memory:",  # Use memory layer
        }

        try:
            result = processing.run(
                "native:extractbyattribute",
                extract_params,
                context=self.context,
                feedback=feedback,
                is_child_algorithm=True,
            )

            if not result or "OUTPUT" not in result:
                return None

            # Get the output layer
            output_layer = result["OUTPUT"]
            if isinstance(output_layer, str):
                output_layer = self.context.getMapLayer(output_layer)

            if output_layer and output_layer.isValid():
                feature_count = output_layer.featureCount()
                if feature_count > 0:
                    self._log_message(f"✅ Extracted {feature_count:,} features")
                    self.temp_layers.append(output_layer)
                    return output_layer
                else:
                    self._log_message(f"⚠️ No features found for region {region_code}")
                    return None

            return None

        except Exception as e:
            self._log_message(f"Error extracting region: {str(e)}")
            return None

    def _run_mat2_workflow_for_region(
        self,
        region_layer: QgsVectorLayer,
        stamdata_layer: QgsVectorLayer,
        jordstykker_layer: QgsVectorLayer,
        region_code: str,
        feedback: QgsProcessingMultiStepFeedback,
        step_offset: int,
    ) -> Optional[str]:
        """Run MAT2 workflow with proper step tracking"""

        try:
            # Track current step for feedback - updated to 8 steps (added cleaning)
            current_step = (
                step_offset  # NEW STEP 0: Clean numeric data in CSV layers FIRST
            )
            feedback.setCurrentStep(current_step)
            self._log_message("  🧹 Cleaning stamdata numeric formats...")
            stamdata_cleaned = self._clean_numeric_data(stamdata_layer, feedback)
            if not stamdata_cleaned:
                return None

            self._log_message("  🧹 Cleaning jordstykker numeric formats...")
            jordstykker_cleaned = self._clean_numeric_data(jordstykker_layer, feedback)
            if not jordstykker_cleaned:
                return None

            # Test the cleaning results (only for first region to avoid spam)
            if region_code == "1081" or step_offset == 0:
                self._test_data_cleaning(stamdata_cleaned)

            current_step += 1

            # Step 1: Refactor Jordstykker fields (now with cleaned data)
            feedback.setCurrentStep(current_step)
            jordstykker_refactored = self._refactor_fields(
                jordstykker_cleaned,  # Use cleaned version
                self.JORDSTYKKER_FIELD_MAP,
                "Refactoring Jordstykker fields",
                feedback,
            )
            if not jordstykker_refactored:
                return None
            current_step += 1

            # Step 2: Convert text to integer
            feedback.setCurrentStep(current_step)
            region_converted = self._convert_text_to_integer(region_layer, feedback)
            if not region_converted:
                return None
            current_step += 1

            # Step 3: Refactor Stamdata fields (now with cleaned data)
            feedback.setCurrentStep(current_step)
            stamdata_refactored = self._refactor_fields(
                stamdata_cleaned,  # Use cleaned version
                self.STAMDATA_FIELD_MAP,
                "Refactoring Stamdata fields",
                feedback,
            )
            if not stamdata_refactored:
                return None
            current_step += 1

            # Step 4: Join region with stamdata
            feedback.setCurrentStep(current_step)
            join1_result = self._perform_join(
                region_converted,
                stamdata_refactored,
                "samletfastejendomlokalid",
                "BFE-nummer",
                "Joining with stamdata",
                feedback,
            )
            if not join1_result:
                return None
            current_step += 1

            # Step 5: Join with jordstykker
            feedback.setCurrentStep(current_step)
            join2_result = self._perform_join(
                join1_result,
                jordstykker_refactored,
                "lokalid",
                "JordstykkeID",
                "Joining with jordstykker",
                feedback,
                one_to_many=True,
            )
            if not join2_result:
                return None
            current_step += 1

            # Step 6: Final field setup
            feedback.setCurrentStep(current_step)
            final_fields = self._setup_final_fields(join2_result, feedback)
            if not final_fields:
                return None
            current_step += 1
            # Step 7: Extract non-null features
            feedback.setCurrentStep(current_step)
            final_output = self._extract_non_null_features(
                final_fields, region_code, feedback
            )

            return final_output

        except Exception as e:
            self._log_message(f"Error in MAT2 workflow: {str(e)}")
            return None

    def _clean_numeric_data(
        self,
        input_layer: Union[QgsVectorLayer, str],
        feedback: QgsProcessingMultiStepFeedback,
    ) -> Optional[Union[QgsVectorLayer, str]]:
        """Clean numeric data by removing formatting and converting dashes to NULL"""

        self._log_message("  Cleaning numeric data format...")

        # Get list of all fields to clean
        if isinstance(input_layer, QgsVectorLayer):
            layer = input_layer
        else:
            layer = QgsProcessingUtils.mapLayerFromString(input_layer, self.context)

        fields_mapping = []
        for field in layer.fields():
            field_name = field.name()

            # Identify numeric fields that need cleaning
            numeric_area_fields = [
                "Grundareal",
                "Registreret areal",
                "Vejareal",
                "Fredskovsareal",
                "Strandbeskyttelsesareal",
                "Brugsretsareal",
                "Beboelsesareal",
                "Erhvervsareal",
                "Bygningsareal",
                "Enhedsareal - Beboelse",
                "Enhedsareal - Erhverv",
                "Areal af udnyttet del af tagetage",
                "Kælderareal",
                "Tinglyst areal (Ejerlejligheder)",
            ]

            price_fields = [
                "Offentlig ejendomsværdi (Ny)",
                "Offentlig grundværdi (Ny)",
                "Seneste handelspris",
                "Pris pr. m2 (BR18-§455 etageareal)",
                "Offentlig ejendomsværdi (Historisk)",
                "Offentlig grundværdi (Historisk)",
                "Offentlig ejendomsværdi (Foreløbig)",
                "Offentlig grundværdi (Foreløbig)",
                "Grundskyld (Historisk)",
                "Dækningsafgift (Historisk)",
                "Ejendomsværdiskat (Historisk)",
                "Totale skat (Historisk)",
                "Grundskyld (Ny)",
                "Ejendomsværdiskat (Ny)",
                "Totale skat (Ny)",
                "Grundskyld (Foreløbig)",
                "Ejendomsværdiskat (Foreløbig)",
                "Totale skat (Foreløbig)",
                "Pris pr. m2 (samlet enhedsareal)",
                "Pris pr. m2 (enhedsareal)",
                "Pris pr. m2 (grund)",
            ]

            percentage_fields = [
                "Realiseret bebyggelsesprocent",
                "Seneste handlet andel",
            ]

            integer_fields = [
                "JordstykkeID",
                "BFE-nummer",
                "Beboelsesenheder",
                "Erhvervsenheder",
                "Opførelsesår",
                "Antal ejere",
                "Primær ejer alder",
                "Vurderingsår (Historisk)",
                "Vurderingsår (Ny)",
                "Vurderingsår (Foreløbig)",
                "Etageareal (BR18-§455)",
                "Antal værelser",
                "Omtilbygningsår",
                "Fordelingstal (Tæller)",
                "Fordelingstal (Nævner)",
                "Postnr",
                "Primær ejer postnr.",
                "Kommunekode",
                "Ejendomsnummer",
            ]  # Create appropriate cleaning expression based on field type
            if field_name in numeric_area_fields:
                # Clean area fields: remove spaces, replace commas with dots, handle dashes
                expression = f"""CASE 
                    WHEN trim("{field_name}") = '-' OR trim("{field_name}") = '' OR "{field_name}" IS NULL 
                    THEN NULL
                    ELSE to_real(regexp_replace(regexp_replace(trim("{field_name}"), ' ', ''), ',', '.'))
                END"""
                field_type = QVariant.Double
                precision = 2

            elif field_name in price_fields:
                # Clean price fields: remove "kr.", spaces, commas (all commas for thousand separators), handle dashes
                expression = f"""CASE 
                    WHEN trim("{field_name}") IN ('-', '- kr.', '') OR "{field_name}" IS NULL 
                    THEN NULL
                    ELSE to_real(
                        regexp_replace(
                            regexp_replace(
                                regexp_replace(trim("{field_name}"), ' kr\\.', ''),
                                ',', ''
                            ),
                            ' ', ''
                        )
                    )
                END"""                field_type = QVariant.Double
                precision = 2

            elif field_name in percentage_fields:
                # Clean percentage fields: remove %, spaces, handle dashes
                expression = f"""CASE 
                    WHEN trim("{field_name}") IN ('-', '-%', '') OR "{field_name}" IS NULL 
                    THEN NULL
                    ELSE to_real(regexp_replace(trim("{field_name}"), '%', ''))
                END"""
                field_type = QVariant.Double
                precision = 2

            elif field_name in integer_fields:
                # Clean integer fields: handle dashes, spaces, boolean-like strings, and ensure proper integer conversion
                expression = f"""CASE 
                    WHEN trim("{field_name}") = '-' OR trim("{field_name}") = '' OR "{field_name}" IS NULL 
                    THEN NULL
                    WHEN lower(trim("{field_name}")) IN ('true', 'ja', 'yes', 'sand', '1') 
                    THEN 1
                    WHEN lower(trim("{field_name}")) IN ('false', 'nej', 'no', 'falsk', '0') 
                    THEN 0
                    WHEN length(trim("{field_name}")) > 0
                    THEN to_int(regexp_replace(trim("{field_name}"), ' ', ''))
                    ELSE NULL
                END"""
                field_type = QVariant.Int
                precision = 0

            else:
                # Keep as string, just clean whitespace and handle dashes
                expression = f"""CASE 
                    WHEN trim("{field_name}") = '-' 
                    THEN NULL
                    ELSE trim("{field_name}")
                END"""
                field_type = QVariant.String
                precision = 0

            field_map = {
                "alias": "",
                "comment": "",
                "expression": expression,
                "length": field.length(),
                "name": field_name,
                "precision": precision,
                "sub_type": 0,
                "type": field_type,
                "type_name": QVariant.typeToName(field_type),
            }
            fields_mapping.append(field_map)

        params = {
            "INPUT": input_layer,
            "FIELDS_MAPPING": fields_mapping,
            "OUTPUT": "memory:",
        }

        try:
            result = processing.run(
                "native:refactorfields",
                params,
                context=self.context,
                feedback=feedback,
                is_child_algorithm=True,
            )

            if result and "OUTPUT" in result:
                self._track_temp_layer(result["OUTPUT"])
                return result["OUTPUT"]

            return None

        except Exception as e:
            self._log_message(f"  ❌ Data cleaning failed: {str(e)}")
            return None

    def _refactor_fields(
        self,
        input_layer: Union[QgsVectorLayer, str],
        field_mapping: List[Dict[str, Any]],
        description: str,
        feedback: QgsProcessingMultiStepFeedback,
    ) -> Optional[Union[QgsVectorLayer, str]]:
        """Refactor fields with proper type handling"""

        self._log_message(f"  {description}")

        # Convert field mapping to algorithm format
        fields_mapping = []
        for field_def in field_mapping:
            field_map = {
                "alias": "",
                "comment": "",
                "expression": field_def["expression"],
                "length": field_def["length"],
                "name": field_def["name"],
                "precision": field_def["precision"],
                "sub_type": 0,
                "type": field_def["type"],
                "type_name": QVariant.typeToName(field_def["type"]),
            }
            fields_mapping.append(field_map)

        params = {
            "INPUT": input_layer,
            "FIELDS_MAPPING": fields_mapping,
            "OUTPUT": "memory:",
        }

        try:
            result = processing.run(
                "native:refactorfields",
                params,
                context=self.context,
                feedback=feedback,
                is_child_algorithm=True,
            )

            if result and "OUTPUT" in result:
                self._track_temp_layer(result["OUTPUT"])
                return result["OUTPUT"]

            return None

        except Exception as e:
            self._log_message(f"  ❌ Refactor failed: {str(e)}")
            return None

    def _convert_text_to_integer(
        self,
        input_layer: Union[QgsVectorLayer, str],
        feedback: QgsProcessingMultiStepFeedback,
    ) -> Optional[Union[QgsVectorLayer, str]]:
        """Convert text fields to integer with validation"""

        self._log_message("  Converting text fields to integer")

        field_mapping = [
            {
                "alias": "",
                "comment": "",
                "expression": '"samletfastejendomlokalid"',
                "length": 36,
                "name": "samletfastejendomlokalid",
                "precision": 0,
                "sub_type": 0,
                "type": QVariant.Int,
                "type_name": "integer",
            },
            {
                "alias": "",
                "comment": "",
                "expression": '"lokalid"',
                "length": 36,
                "name": "lokalid",
                "precision": 0,
                "sub_type": 0,
                "type": QVariant.Int,
                "type_name": "integer",
            },
        ]

        return self._refactor_fields(
            input_layer, field_mapping, "Converting to integer", feedback
        )

    def _perform_join(
        self,
        input1: Union[QgsVectorLayer, str],
        input2: Union[QgsVectorLayer, str],
        field1: str,
        field2: str,
        description: str,
        feedback: QgsProcessingMultiStepFeedback,
        one_to_many: bool = False,
    ) -> Optional[Union[QgsVectorLayer, str]]:
        """Perform join with statistics logging"""

        self._log_message(f"  {description}")

        params = {
            "INPUT": input1,
            "INPUT_2": input2,
            "FIELD": field1,
            "FIELD_2": field2,
            "FIELDS_TO_COPY": [""],  # Copy all fields
            "METHOD": 0 if one_to_many else 1,  # Join type
            "DISCARD_NONMATCHING": False,  # Keep unmatched
            "PREFIX": "",
            "OUTPUT": "memory:",
        }

        try:
            result = processing.run(
                "native:joinattributestable",
                params,
                context=self.context,
                feedback=feedback,
                is_child_algorithm=True,
            )

            if result:
                # Log statistics
                joined = result.get("JOINED_COUNT", 0)
                unjoined = result.get("UNJOINABLE_COUNT", 0)
                self._log_message(f"    Joined: {joined:,} | Unmatched: {unjoined:,}")

                if "OUTPUT" in result:
                    self._track_temp_layer(result["OUTPUT"])
                    return result["OUTPUT"]

            return None

        except Exception as e:
            self._log_message(f"  ❌ Join failed: {str(e)}")
            return None

    def _setup_final_fields(
        self,
        input_layer: Union[QgsVectorLayer, str],
        feedback: QgsProcessingMultiStepFeedback,
    ) -> Optional[Union[QgsVectorLayer, str]]:
        """Setup final output fields in logical order with complete land attributes"""

        # Organize fields logically for optimal analysis workflow
        final_field_mapping = [
            # 1. Property Identifiers
            {"expression": '"BFE-nummer"', "name": "BFE-nummer", "type": QVariant.Int},
            {
                "expression": '"JordstykkeID"',
                "name": "JordstykkeID",
                "type": QVariant.Int,
            },
            # 2. Property Location and Coordinates
            {
                "expression": '"Matrikelnr"',
                "name": "Matrikelnr",
                "type": QVariant.String,
            },
            {
                "expression": '"Ejerlavsnavn"',
                "name": "Ejerlavsnavn",
                "type": QVariant.String,
            },
            {
                "expression": "regexp_replace(to_string(\"Longitude\", 6), '\\\\.',  ',')",
                "name": "Longitude",
                "type": QVariant.String,
                "precision": 0,
            },
            {
                "expression": "regexp_replace(to_string(\"Latitude\", 6), '\\\\.',  ',')",
                "name": "Latitude",
                "type": QVariant.String,
                "precision": 0,
            },
            # 3. Ownership Information
            {
                "expression": '"Antal ejere"',
                "name": "Antal ejere",
                "type": QVariant.Int,
            },
            {
                "expression": '"Primær ejer"',
                "name": "Primær ejer",
                "type": QVariant.String,
            },
            {
                "expression": '"Primær ejer alder"',
                "name": "Primær ejer alder",
                "type": QVariant.String,
            },
            # 4. Address Information
            {
                "expression": '"Postlinje 1"',
                "name": "Postlinje 1",
                "type": QVariant.String,
            },
            {"expression": '"Postnr"', "name": "Postnr", "type": QVariant.String},
            {"expression": '"By"', "name": "By", "type": QVariant.String},
            # 5. Property Characteristics
            {
                "expression": '"Anvendelse"',
                "name": "Anvendelse",
                "type": QVariant.String,
            },
            {"expression": '"Type"', "name": "Type", "type": QVariant.String},
            {"expression": '"Undertype"', "name": "Undertype", "type": QVariant.String},
            {
                "expression": '"Opførelsesår"',
                "name": "Opførelsesår",
                "type": QVariant.String,
            },
            {
                "expression": '"Energimærke"',
                "name": "Energimærke",
                "type": QVariant.String,
            },
            {
                "expression": '"Er fælleslod"',
                "name": "Er fælleslod",
                "type": QVariant.String,
            },
            # 6. Property Values
            {
                "expression": '"Seneste handelspris"',
                "name": "Seneste handelspris",
                "type": QVariant.String,
            },
            {
                "expression": '"Seneste handelsdato"',
                "name": "Seneste handelsdato",
                "type": QVariant.String,
            },
            {
                "expression": '"Offentlig ejendomsværdi (Ny)"',
                "name": "Offentlig ejendomsværdi (Ny)",
                "type": QVariant.String,
            },
            # 7. Area and Building Information
            {
                "expression": '"Realiseret bebyggelsesprocent"',
                "name": "Realiseret bebyggelsesprocent",
                "type": QVariant.String,
            },
            {
                "expression": '"Grundareal"',
                "name": "Grundareal",
                "type": QVariant.String,
            },
            {
                "expression": "regexp_replace(to_string(round($area / 10000, 2), 2), '\\\\.',  ',')",
                "name": "Areal_ha",
                "type": QVariant.String,
                "precision": 0,
            },
            # 8. Environmental Protection Areas
            {"expression": '"Vejareal"', "name": "Vejareal", "type": QVariant.String},
            {
                "expression": '"Fredskovsareal"',
                "name": "Fredskovsareal",
                "type": QVariant.String,
            },
            {
                "expression": '"Fredskovsareal omfang"',
                "name": "Fredskovsareal omfang",
                "type": QVariant.String,
            },
            {
                "expression": '"Strandbeskyttelsesareal"',
                "name": "Strandbeskyttelsesareal",
                "type": QVariant.String,
            },
            {
                "expression": '"Strandbeskyttelse omfang"',
                "name": "Strandbeskyttelse omfang",
                "type": QVariant.String,
            },
        ]

        # Add length and precision
        for field in final_field_mapping:
            if "length" not in field:
                field["length"] = 0
            if "precision" not in field:
                field["precision"] = 0

        return self._refactor_fields(
            input_layer,
            final_field_mapping,
            "Setting up final fields with complete land attributes",
            feedback,
        )

    def _extract_non_null_features(
        self,
        input_layer: Union[QgsVectorLayer, str],
        region_code: str,
        feedback: QgsProcessingMultiStepFeedback,
    ) -> Optional[str]:
        """Extract non-NULL features and save to file"""

        self._log_message("  Extracting non-NULL features")

        # Use QGIS temp file generation for proper cleanup
        output_file = QgsProcessingUtils.generateTempFilename(
            f"region_{region_code}_output.gpkg"
        )

        params = {
            "INPUT": input_layer,
            "FIELD": "JordstykkeID",  # Fixed: removed expression evaluation
            "OPERATOR": 9,  # is not null
            "VALUE": "",
            "OUTPUT": output_file,
        }

        try:
            result = processing.run(
                "native:extractbyattribute",
                params,
                context=self.context,
                feedback=feedback,
                is_child_algorithm=True,
            )

            if result and "OUTPUT" in result:
                self.temp_files.append(output_file)

                # Validate output
                test_layer = QgsVectorLayer(output_file, "test", "ogr")
                if test_layer.isValid():
                    count = test_layer.featureCount()
                    self._log_message(f"    Output: {count:,} features")

                return output_file

            return None

        except Exception as e:
            self._log_message(f"  ❌ Extract failed: {str(e)}")
            return None

    def _merge_regional_outputs(
        self,
        regional_outputs: List[str],
        parameters: Dict[str, Any],
        feedback: QgsProcessingMultiStepFeedback,
    ) -> Dict[str, Any]:
        """Merge regional outputs with validation"""

        self._log_message("\n" + "=" * 60)
        self._log_message("MERGING REGIONAL RESULTS")
        self._log_message("=" * 60)

        # Load and validate layers
        layers_to_merge = []
        total_features = 0

        for output_path in regional_outputs:
            layer = QgsVectorLayer(output_path, "region_layer", "ogr")
            if layer.isValid():
                count = layer.featureCount()
                layers_to_merge.append(layer)
                total_features += count
                self._log_message(
                    f"  Loading: {os.path.basename(output_path)} ({count:,} features)"
                )

        if not layers_to_merge:
            raise QgsProcessingException("No valid layers to merge")

        self._log_message(
            f"\nMerging {len(layers_to_merge)} layers ({total_features:,} total features)"
        )

        # Get CRS from first layer
        dest_crs = layers_to_merge[0].crs()
        self._log_message(f"Using destination CRS {dest_crs.authid()}")
        self._log_message(
            f"Setting output type to {QgsWkbTypes.displayString(layers_to_merge[0].wkbType())}"
        )

        # First, merge all layers to a temporary output
        temp_merge = QgsProcessingUtils.generateTempFilename("merged_temp.gpkg")

        merge_params = {
            "LAYERS": layers_to_merge,
            "CRS": dest_crs,
            "OUTPUT": temp_merge,
        }

        try:
            result = processing.run(
                "native:mergevectorlayers",
                merge_params,
                context=self.context,
                feedback=feedback,
                is_child_algorithm=True,
            )

            if not result or "OUTPUT" not in result:
                raise QgsProcessingException("Merge operation returned no output")

            # Load merged layer to filter fields
            merged_layer = QgsVectorLayer(temp_merge, "merged", "ogr")
            if not merged_layer.isValid():
                raise QgsProcessingException("Failed to load merged layer")

            # Build field mapping, excluding 'layer' and 'path' fields
            fields_to_exclude = ["layer", "path"]
            field_mapping = []

            for field in merged_layer.fields():
                if field.name() not in fields_to_exclude:
                    field_mapping.append(
                        {
                            "expression": f'"{field.name()}"',
                            "length": field.length(),
                            "name": field.name(),
                            "precision": field.precision(),
                            "type": field.type(),
                        }
                    )

            self._log_message(
                f"Removing processing artifacts: {', '.join(fields_to_exclude)}"
            )

            # Parse the output parameter properly
            output_param = parameters[self.OUTPUT]

            # Check if it's a GeoPackage layer specification
            if isinstance(output_param, str) and "|layername=" in output_param:
                # Parse GeoPackage path and layer name
                parts = output_param.split("|layername=")
                gpkg_path = parts[0]
                layer_name = parts[1] if len(parts) > 1 else "lodsejerinformation_rs"
                output_spec = f"{gpkg_path}|layername={layer_name}"

                self._log_message(f"Output GeoPackage: {gpkg_path}")
                self._log_message(f"Output layer: {layer_name}")
            else:
                # Regular file output
                output_spec = output_param

            # Use refactorfields to create final output without unwanted fields
            refactor_params = {
                "INPUT": merged_layer,
                "FIELDS_MAPPING": field_mapping,
                "OUTPUT": output_spec,
            }

            final_result = processing.run(
                "native:refactorfields",
                refactor_params,
                context=self.context,
                feedback=feedback,
                is_child_algorithm=True,
            )

            if final_result and "OUTPUT" in final_result:
                self._log_message(f"✅ Merge completed successfully")

                # Validate final output
                if isinstance(final_result["OUTPUT"], str):
                    # It's a path, validate it
                    if "|layername=" in final_result["OUTPUT"]:
                        # GeoPackage output
                        test_path = final_result["OUTPUT"].split("|layername=")[0]
                        if os.path.exists(test_path):
                            self._log_message(f"📊 Output created: {test_path}")
                    else:
                        # Regular file
                        if os.path.exists(final_result["OUTPUT"]):
                            self._log_message(
                                f"📊 Output created: {final_result['OUTPUT']}"
                            )

                # Track temp file for cleanup
                self.temp_files.append(temp_merge)

                return {self.OUTPUT: final_result["OUTPUT"]}
            else:
                raise QgsProcessingException("Failed to create final output")

        except Exception as e:
            self._log_message(f"❌ Error during merge: {str(e)}", Qgis.Critical)
            raise QgsProcessingException(f"Failed to merge regional outputs: {str(e)}")
        finally:
            # Cleanup layer references to avoid locks
            for layer in layers_to_merge:
                if hasattr(layer, "deleteLater"):
                    layer.deleteLater()

    def _process_standard_mat2(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Process without regional split"""

        self._log_message("Running standard MAT2 processing (no regional split)")

        # Get jordstykke layer
        jordstykke_layer = self.parameterAsVectorLayer(
            parameters, self.INPUT_JORDSTYKKE, self.context
        )

        # Load CSV files as layers
        stamdata_path = self.parameterAsFile(
            parameters, self.INPUT_STAMDATA, self.context
        )
        jordstykker_path = self.parameterAsFile(
            parameters, self.INPUT_JORDSTYKKER, self.context
        )

        self._log_message("\n📁 Loading CSV data files...")
        stamdata_layer = self._load_csv_as_layer(stamdata_path, "stamdata_rs")
        jordstykker_layer = self._load_csv_as_layer(
            jordstykker_path, "jordstykker_rs"
        )  # Create simple feedback for single region
        feedback = QgsProcessingMultiStepFeedback(
            8, self.feedback
        )  # Updated to 8 steps (added cleaning)

        # Run workflow
        output_file = self._run_mat2_workflow_for_region(
            jordstykke_layer, stamdata_layer, jordstykker_layer, "full", feedback, 0
        )

        if not output_file:
            raise QgsProcessingException(
                "Standard processing failed - no output generated"
            )

        # Load the output file
        output_layer = QgsVectorLayer(output_file, "output", "ogr")
        if not output_layer.isValid():
            raise QgsProcessingException("Failed to load output file")

        # Build field mapping, excluding any 'layer' and 'path' fields if they exist
        fields_to_exclude = ["layer", "path"]
        field_mapping = []

        for field in output_layer.fields():
            if field.name() not in fields_to_exclude:
                field_mapping.append(
                    {
                        "expression": f'"{field.name()}"',
                        "length": field.length(),
                        "name": field.name(),
                        "precision": field.precision(),
                        "type": field.type(),
                    }
                )

        # Check if we need to exclude any fields
        excluded_fields = [
            f for f in fields_to_exclude if f in output_layer.fields().names()
        ]
        if excluded_fields:
            self._log_message(
                f"Removing processing artifacts: {', '.join(excluded_fields)}"
            )

        # Parse output parameter to handle GeoPackage correctly
        output_param = parameters[self.OUTPUT]

        if isinstance(output_param, str) and "|layername=" in output_param:
            # GeoPackage output
            parts = output_param.split("|layername=")
            gpkg_path = parts[0]
            layer_name = parts[1] if len(parts) > 1 else "lodsejerinformation_rs"
            output_spec = f"{gpkg_path}|layername={layer_name}"
        else:
            # Regular output
            output_spec = output_param

        # Use refactor fields to create clean output
        refactor_params = {
            "INPUT": output_layer,
            "FIELDS_MAPPING": field_mapping,
            "OUTPUT": output_spec,
        }

        result = processing.run(
            "native:refactorfields",
            refactor_params,
            context=self.context,
            feedback=self.feedback,
            is_child_algorithm=True,
        )

        if result and "OUTPUT" in result:
            return {self.OUTPUT: result["OUTPUT"]}
        else:
            raise QgsProcessingException("Failed to create final output")

    def _track_temp_layer(self, layer: Union[QgsVectorLayer, str]) -> None:
        """Track temporary layer for cleanup"""

        if isinstance(layer, str):
            # It's a layer ID or path
            actual_layer = self.context.getMapLayer(layer)
            if actual_layer:
                self.temp_layers.append(actual_layer)
            # If it's a file path, track it for cleanup
            elif os.path.exists(layer):
                self.temp_files.append(layer)
        elif isinstance(layer, QgsVectorLayer):
            self.temp_layers.append(layer)

    def _cleanup_resources(self) -> None:
        """Enhanced cleanup with proper resource management"""

        try:
            self._log_message("\n🧹 Cleaning up resources...")

            # Clear layer references
            for layer in self.temp_layers:
                if layer and hasattr(layer, "deleteLater"):
                    layer.deleteLater()

            self.temp_layers.clear()

            # Force garbage collection
            gc.collect()

            # Clean temp files with retry
            if self.temp_files:
                cleaned = 0
                for temp_file in self.temp_files:
                    if self._delete_file_with_retry(temp_file):
                        cleaned += 1

                self._log_message(
                    f"  Cleaned {cleaned}/{len(self.temp_files)} temp files"
                )

            # Remove workspace directory if empty
            if self.temp_workspace and os.path.exists(self.temp_workspace):
                try:
                    os.rmdir(self.temp_workspace)
                    self._log_message(f"  Removed temp workspace")
                except OSError:
                    # Directory not empty or in use
                    pass

        except Exception as e:
            self._log_message(f"⚠️ Cleanup warning: {str(e)}")

    def _delete_file_with_retry(self, filepath: str, max_attempts: int = 3) -> bool:
        """Delete file with retry logic for Windows file locks"""

        if not os.path.exists(filepath):
            return True

        for attempt in range(max_attempts):
            try:
                os.remove(filepath)
                return True
            except Exception:
                if attempt < max_attempts - 1:
                    time.sleep(0.5 * (attempt + 1))  # Exponential backoff

        return False

    def _log_message(self, message: str, level: Qgis.MessageLevel = Qgis.Info) -> None:
        """Unified logging with feedback and message log"""

        if self.feedback:
            self.feedback.pushInfo(message)
        QgsMessageLog.logMessage(message, self.displayName(), level)

    def _test_data_cleaning(self, cleaned_layer: Union[QgsVectorLayer, str]) -> None:
        """Test that data cleaning worked properly"""

        self._log_message("\n📊 Testing cleaned data...")

        # Get layer if it's a string
        if isinstance(cleaned_layer, str):
            layer = QgsProcessingUtils.mapLayerFromString(cleaned_layer, self.context)
        else:
            layer = cleaned_layer

        if not layer or not layer.isValid():
            self._log_message("❌ Cannot test cleaning - invalid layer")
            return

        # Get first feature for testing
        features = list(layer.getFeatures())
        if not features:
            self._log_message("❌ No features to test")
            return

        feature = features[0]

        # Test key numeric fields that are commonly problematic
        test_fields = {
            "Grundareal": (QVariant.Double, "numeric area"),
            "Seneste handelspris": (QVariant.Double, "price"),
            "Realiseret bebyggelsesprocent": (QVariant.Double, "percentage"),
            "Opførelsesår": (QVariant.Int, "year"),
            "BFE-nummer": (QVariant.Int, "ID"),
            "JordstykkeID": (QVariant.Int, "ID"),
            "Longitude": (QVariant.Double, "coordinate"),
            "Latitude": (QVariant.Double, "coordinate"),
        }

        for field_name, (expected_type, field_desc) in test_fields.items():
            field = layer.fields().field(field_name)
            if field:
                actual_type = field.type()
                value = feature[field_name]

                # Check type
                type_ok = actual_type == expected_type
                type_symbol = "✅" if type_ok else "❌"

                # Check value
                if value is None:
                    value_desc = "NULL (cleaned from '-')"
                elif isinstance(value, (int, float)):
                    if isinstance(value, float):
                        value_desc = f"{value:,.2f}"
                    else:
                        value_desc = str(value)
                else:
                    value_desc = f"'{value}' (string - needs cleaning!)"

                self._log_message(
                    f"  {type_symbol} {field_name} ({field_desc}): "
                    f"Type={QVariant.typeToName(actual_type)}, "
                    f"Value={value_desc}"
                )

        # Summary statistics
        self._log_message("\n📈 Cleaning summary:")

        # Count NULLs in key fields to verify dash conversion worked
        null_test_fields = ["Grundareal", "Seneste handelspris", "Fredskovsareal"]
        for field_name in null_test_fields:
            if field_name in layer.fields().names():
                null_count = 0
                total_count = 0

                for feat in layer.getFeatures():
                    total_count += 1
                    if feat[field_name] is None:
                        null_count += 1

                null_pct = (null_count / total_count * 100) if total_count > 0 else 0
                self._log_message(
                    f"  {field_name}: {null_count}/{total_count} NULL ({null_pct:.1f}%)"
                )

    # Algorithm metadata
    def name(self) -> str:
        return "RS_join_model_MAT2_Regional"

    def displayName(self) -> str:
        return "RS_join_model_MAT2 (Regional Processing with Complete Land Attributes)"

    def group(self) -> str:
        return "join_model"

    def groupId(self) -> str:
        return "join_model"

    def createInstance(self) -> "EnhancedRsJoinModelMat2Regional":
        return EnhancedRsJoinModelMat2Regional()

    def shortHelpString(self) -> str:
        return """
        <h3>Enhanced Regional Processing for RS_join_model_MAT2</h3>
        
        <p>This algorithm replicates the exact workflow of the original MAT2 model
        but adds regional processing capabilities for handling large datasets, now with
        complete land-related attributes for comprehensive property analysis.</p>
        
        <h4>Input Requirements:</h4>
        <ul>
        <li><b>Jordstykke layer</b>: Cadastral polygons with regionskode field</li>
        <li><b>Stamdata CSV</b>: Property records with BFE-nummer field and property data</li>
        <li><b>Jordstykker CSV</b>: Parcel records with JordstykkeID, coordinates, and land attributes</li>
        </ul>
        
        <h4>Output Fields (Comprehensively Organized):</h4>
        <ul>
        <li><b>Identifiers</b>: BFE-nummer, JordstykkeID</li>
        <li><b>Location & Coordinates</b>: Matrikelnr, Ejerlavsnavn, Longitude, Latitude</li>
        <li><b>Ownership</b>: Antal ejere, Primær ejer, Primær ejer alder</li>
        <li><b>Address</b>: Postlinje 1, Postnr, By</li>
        <li><b>Property Info</b>: Anvendelse, Type, Undertype, Opførelsesår, Energimærke, Er fælleslod</li>
        <li><b>Values</b>: Seneste handelspris/dato, Offentlig ejendomsværdi (Ny)</li>
        <li><b>Area Data</b>: Realiseret bebyggelsesprocent, Grundareal, Areal_ha (calculated)</li>
        <li><b>Environmental Protection</b>: Vejareal, Fredskovsareal/omfang, Strandbeskyttelsesareal/omfang</li>
        </ul>
        
        <h4>Output Options:</h4>
        <ul>
        <li><b>GeoPackage</b>: Select existing .gpkg file and specify layer name</li>
        <li><b>Shapefile</b>: Specify .shp output path</li>
        <li><b>Temporary layer</b>: Leave empty for memory layer output</li>
        </ul>
          <h4>Features:</h4>
        <ul>
        <li>Enhanced MAT2 workflow with data cleaning (8 steps per region)</li>
        <li>Automatic Danish numeric format cleaning (commas, currency, nulls)</li>
        <li>Regional splitting for performance optimization</li>
        <li>Enhanced temp file management with automatic cleanup</li>
        <li>Proper NULL handling (expected join failures)</li>
        <li>GeoPackage and Shapefile output support</li>
        <li>Automatic removal of processing artifact fields ('layer', 'path')</li>
        <li>Calculated area field (Areal_ha) for standardized measurements</li>
        <li>Complete land attributes including environmental protections</li>
        <li>Spatial coordinates from parcel-specific data (Jordstykker)</li>
        </ul>
        
        <h4>Regional Processing:</h4>
        <ul>
        <li>Splits jordstykke layer by region codes</li>
        <li>Runs complete MAT2 workflow for each region</li>
        <li>Merges results into single output</li>
        </ul>
        
        <h4>Expected Behavior:</h4>
        <p>The CSV files typically contain subset records for filtering.
        JOIN failures create NULL values when features lack CSV matches.
        This is intentional filtering behavior. The final step removes 
        NULL JordstykkeID features, resulting in only matched features.
        Coordinates are taken from Jordstykker_RS as they are parcel-specific.</p>
        
        <h4>Performance Tips:</h4>
        <ul>
        <li>Use regional processing for large datasets</li>
        <li>Provide a fast SSD location for temp directory (optional)</li>
        <li>Monitor memory usage - each region processes independently</li>
        <li>Check CSV delimiter (comma, semicolon, or tab) if loading fails</li>
        </ul>
        
        <h4>Troubleshooting:</h4>
        <ul>
        <li>If temp files aren't cleaned, check the temp directory manually</li>
        <li>For GeoPackage output errors, ensure the .gpkg file exists first</li>
        <li>CSV loading issues: check field names and delimiters</li>
        <li>Missing values in financial fields show as '- kr.' in the data</li>
        <li>Area fields with commas/spaces are treated as strings; use Areal_ha for calculations</li>
        </ul>
        
        <h4>Data Quality Notes:</h4>
        <ul>
        <li>Protection area fields (fredskov, strandbeskyttelse) are well-populated</li>
        <li>Registreret areal contains special characters; Areal_ha is more reliable</li>
        <li>Coordinates from Jordstykker are plot-specific and preferred over Stamdata</li>
        </ul>
        """

    def helpUrl(self) -> str:
        """Return help URL"""
        return "https://your-documentation-url.com/rs_join_model_mat2_regional"

    def icon(self) -> Any:
        """Return algorithm icon"""
        # Could return a QIcon if you have one
        return super().icon()
