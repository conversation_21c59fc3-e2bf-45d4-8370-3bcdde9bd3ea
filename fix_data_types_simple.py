"""
Simple Data Type Fixer for test123.gpkg

This script uses ogr/gdal Python bindings to fix data type issues
in the test123.gpkg file. It creates a new layer with corrected data types.
"""

import os
import sys

try:
    from osgeo import ogr, osr
    print("✅ GDAL/OGR imported successfully")
except ImportError:
    print("❌ GDAL/OGR not available. Please install GDAL.")
    sys.exit(1)

def convert_danish_decimal(value):
    """Convert Danish decimal format (comma) to float"""
    if value is None or value.strip() == '' or value.strip() == '-':
        return None
    try:
        # Replace comma with dot and convert to float
        return float(value.replace(',', '.'))
    except (ValueError, AttributeError):
        return None

def convert_to_int(value):
    """Convert string to integer, handling Danish format"""
    if value is None or value.strip() == '' or value.strip() == '-':
        return None
    try:
        return int(value.strip())
    except (ValueError, AttributeError):
        return None

def fix_data_types():
    """Fix data types in test123.gpkg"""
    
    print("=" * 60)
    print("🔧 FIXING DATA TYPES IN test123.gpkg")
    print("=" * 60)
    
    # Check if input file exists
    input_file = "test123.gpkg"
    if not os.path.exists(input_file):
        print(f"❌ File not found: {input_file}")
        return False
    
    print(f"📁 Input file: {input_file}")
    
    # Open input dataset
    input_ds = ogr.Open(input_file, 0)  # 0 = read-only
    if input_ds is None:
        print("❌ Failed to open input file")
        return False
    
    # Get the test123 layer
    input_layer = input_ds.GetLayerByName("test123")
    if input_layer is None:
        print("❌ Layer 'test123' not found")
        input_ds = None
        return False
    
    print(f"✅ Loaded layer with {input_layer.GetFeatureCount()} features")
    
    # Create output dataset
    output_file = "test123_fixed.gpkg"
    if os.path.exists(output_file):
        os.remove(output_file)
    
    driver = ogr.GetDriverByName("GPKG")
    output_ds = driver.CreateDataSource(output_file)
    if output_ds is None:
        print("❌ Failed to create output file")
        input_ds = None
        return False
    
    # Get spatial reference from input layer
    srs = input_layer.GetSpatialRef()
    
    # Create output layer with same geometry type and SRS
    geom_type = input_layer.GetGeomType()
    output_layer = output_ds.CreateLayer("test123_fixed", srs, geom_type)
    
    if output_layer is None:
        print("❌ Failed to create output layer")
        input_ds = None
        output_ds = None
        return False
    
    # Define field definitions with correct types
    field_definitions = [
        ("BFE-nummer", ogr.OFTInteger),
        ("JordstykkeID", ogr.OFTInteger),
        ("Matrikelnr", ogr.OFTString),
        ("Ejerlavsnavn", ogr.OFTString),
        ("longitude", ogr.OFTReal),  # Fixed: Double precision
        ("latitude", ogr.OFTReal),   # Fixed: Double precision
        ("Antal ejere", ogr.OFTInteger),
        ("Primær ejer", ogr.OFTString),
        ("primær_ejer_alder", ogr.OFTInteger),  # Fixed: Integer
        ("Postlinje 1", ogr.OFTString),
        ("postnr", ogr.OFTInteger),  # Fixed: Integer
        ("By", ogr.OFTString),
        ("Anvendelse", ogr.OFTString),
        ("Type", ogr.OFTString),
        ("Undertype", ogr.OFTString),
        ("opførelsesår", ogr.OFTInteger),  # Fixed: Integer
        ("Energimærke", ogr.OFTString),
        ("Er fælleslod", ogr.OFTString),
        ("Seneste handelspris", ogr.OFTReal),
        ("Seneste handelsdato", ogr.OFTString),
        ("Offentlig ejendomsværdi (Ny)", ogr.OFTReal),
        ("Realiseret bebyggelsesprocent", ogr.OFTReal),
        ("Grundareal", ogr.OFTReal),
        ("areal_ha", ogr.OFTReal),  # Fixed: Double precision
        ("Vejareal", ogr.OFTReal),
        ("Fredskovsareal", ogr.OFTReal),
        ("Fredskovsareal omfang", ogr.OFTString),
        ("Strandbeskyttelsesareal", ogr.OFTReal),
        ("Strandbeskyttelse omfang", ogr.OFTString),
    ]
    
    # Create fields in output layer
    print("\n🔧 Creating output fields with correct types:")
    for field_name, field_type in field_definitions:
        field_def = ogr.FieldDefn(field_name, field_type)
        if field_type == ogr.OFTReal:
            field_def.SetPrecision(6 if 'longitude' in field_name.lower() or 'latitude' in field_name.lower() else 2)
        output_layer.CreateField(field_def)
        type_name = "Real" if field_type == ogr.OFTReal else "Integer" if field_type == ogr.OFTInteger else "String"
        print(f"  ✅ {field_name}: {type_name}")
    
    # Copy and convert features
    print(f"\n🔄 Converting {input_layer.GetFeatureCount()} features...")
    
    input_layer.ResetReading()
    converted_count = 0
    error_count = 0
    
    for input_feature in input_layer:
        try:
            # Create new feature
            output_feature = ogr.Feature(output_layer.GetLayerDefn())
            
            # Copy geometry
            geom = input_feature.GetGeometryRef()
            if geom:
                output_feature.SetGeometry(geom.Clone())
            
            # Copy and convert field values
            output_feature.SetField("BFE-nummer", input_feature.GetField("BFE-nummer"))
            output_feature.SetField("JordstykkeID", input_feature.GetField("JordstykkeID"))
            output_feature.SetField("Matrikelnr", input_feature.GetField("Matrikelnr"))
            output_feature.SetField("Ejerlavsnavn", input_feature.GetField("Ejerlavsnavn"))
            
            # Convert coordinates with Danish decimal format
            longitude = convert_danish_decimal(input_feature.GetField("Longitude"))
            latitude = convert_danish_decimal(input_feature.GetField("Latitude"))
            output_feature.SetField("longitude", longitude)
            output_feature.SetField("latitude", latitude)
            
            output_feature.SetField("Antal ejere", input_feature.GetField("Antal ejere"))
            output_feature.SetField("Primær ejer", input_feature.GetField("Primær ejer"))
            
            # Convert age to integer
            age = convert_to_int(input_feature.GetField("Primær ejer alder"))
            output_feature.SetField("primær_ejer_alder", age)
            
            output_feature.SetField("Postlinje 1", input_feature.GetField("Postlinje 1"))
            
            # Convert postal code to integer
            postnr = convert_to_int(input_feature.GetField("Postnr"))
            output_feature.SetField("postnr", postnr)
            
            output_feature.SetField("By", input_feature.GetField("By"))
            output_feature.SetField("Anvendelse", input_feature.GetField("Anvendelse"))
            output_feature.SetField("Type", input_feature.GetField("Type"))
            output_feature.SetField("Undertype", input_feature.GetField("Undertype"))
            
            # Convert year to integer
            year = convert_to_int(input_feature.GetField("Opførelsesår"))
            output_feature.SetField("opførelsesår", year)
            
            output_feature.SetField("Energimærke", input_feature.GetField("Energimærke"))
            output_feature.SetField("Er fælleslod", input_feature.GetField("Er fælleslod"))
            output_feature.SetField("Seneste handelspris", input_feature.GetField("Seneste handelspris"))
            output_feature.SetField("Seneste handelsdato", input_feature.GetField("Seneste handelsdato"))
            output_feature.SetField("Offentlig ejendomsværdi (Ny)", input_feature.GetField("Offentlig ejendomsværdi (Ny)"))
            output_feature.SetField("Realiseret bebyggelsesprocent", input_feature.GetField("Realiseret bebyggelsesprocent"))
            output_feature.SetField("Grundareal", input_feature.GetField("Grundareal"))
            
            # Convert area with Danish decimal format
            areal_ha = convert_danish_decimal(input_feature.GetField("Areal_ha"))
            output_feature.SetField("areal_ha", areal_ha)
            
            output_feature.SetField("Vejareal", input_feature.GetField("Vejareal"))
            output_feature.SetField("Fredskovsareal", input_feature.GetField("Fredskovsareal"))
            output_feature.SetField("Fredskovsareal omfang", input_feature.GetField("Fredskovsareal omfang"))
            output_feature.SetField("Strandbeskyttelsesareal", input_feature.GetField("Strandbeskyttelsesareal"))
            output_feature.SetField("Strandbeskyttelse omfang", input_feature.GetField("Strandbeskyttelse omfang"))
            
            # Add feature to output layer
            output_layer.CreateFeature(output_feature)
            converted_count += 1
            
            # Clean up
            output_feature = None
            
        except Exception as e:
            print(f"  ⚠️ Error converting feature {input_feature.GetFID()}: {str(e)}")
            error_count += 1
            continue
    
    print(f"✅ Converted {converted_count} features")
    if error_count > 0:
        print(f"⚠️ {error_count} features had conversion errors")
    
    # Clean up
    input_ds = None
    output_ds = None
    
    print(f"📁 Output saved to: {output_file}")
    
    # Validate results
    validate_results(output_file)
    
    return True

def validate_results(output_file):
    """Validate the results"""
    
    print(f"\n🔍 Validating results in {output_file}:")
    print("-" * 40)
    
    # Open output file and check structure
    ds = ogr.Open(output_file, 0)
    if ds is None:
        print("❌ Could not open output file for validation")
        return
    
    layer = ds.GetLayerByName("test123_fixed")
    if layer is None:
        print("❌ Could not find test123_fixed layer")
        ds = None
        return
    
    print(f"✅ Layer has {layer.GetFeatureCount()} features")
    
    # Check field types
    layer_defn = layer.GetLayerDefn()
    target_fields = {
        'longitude': ogr.OFTReal,
        'latitude': ogr.OFTReal,
        'areal_ha': ogr.OFTReal,
        'postnr': ogr.OFTInteger,
        'primær_ejer_alder': ogr.OFTInteger,
        'opførelsesår': ogr.OFTInteger
    }
    
    all_correct = True
    for i in range(layer_defn.GetFieldCount()):
        field_defn = layer_defn.GetFieldDefn(i)
        field_name = field_defn.GetName()
        field_type = field_defn.GetType()
        
        if field_name in target_fields:
            expected_type = target_fields[field_name]
            type_name = "Real" if field_type == ogr.OFTReal else "Integer" if field_type == ogr.OFTInteger else "String"
            expected_name = "Real" if expected_type == ogr.OFTReal else "Integer" if expected_type == ogr.OFTInteger else "String"
            
            if field_type == expected_type:
                print(f"  ✅ {field_name}: {type_name}")
            else:
                print(f"  ❌ {field_name}: {type_name} (expected {expected_name})")
                all_correct = False
    
    if all_correct:
        print("\n🎉 All target fields have correct data types!")
    else:
        print("\n⚠️ Some fields still have incorrect data types")
    
    ds = None

if __name__ == "__main__":
    success = fix_data_types()
    if success:
        print("\n🎉 Data type fixing completed successfully!")
        print("📊 Use test123_fixed.gpkg for analysis with correct data types")
    else:
        print("\n❌ Data type fixing failed")
