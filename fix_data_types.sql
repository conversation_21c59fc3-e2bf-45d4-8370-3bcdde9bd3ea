-- SQL script to fix data types in test123.gpkg
-- This creates a new table with corrected data types

CREATE TABLE test123_fixed AS
SELECT 
    fid,
    geom,
    "BFE-nummer",
    JordstykkeID,
    Matrikelnr,
    Ejerlavsnavn,
    
    -- Fix Longitude: Convert Danish decimal format to proper double
    CASE 
        WHEN Longitude IS NULL OR trim(Longitude) = '' 
        THEN NULL 
        ELSE CAST(replace(trim(Longitude), ',', '.') AS REAL)
    END AS Longitude,
    
    -- Fix Latitude: Convert Danish decimal format to proper double  
    CASE 
        WHEN Latitude IS NULL OR trim(Latitude) = '' 
        THEN NULL 
        ELSE CAST(replace(trim(Latitude), ',', '.') AS REAL)
    END AS Latitude,
    
    "Antal ejere",
    "Primær ejer",
    
    -- Fix Primær ejer alder: Convert to integer
    CASE 
        WHEN "Primær ejer alder" IS NULL OR trim("Primær ejer alder") = '' OR trim("Primær ejer alder") = '-'
        THEN NULL 
        ELSE CAST(trim("Primær ejer alder") AS INTEGER)
    END AS "Primær ejer alder",
    
    "Postlinje 1",
    
    -- Fix Postnr: Convert to integer
    CASE 
        WHEN Postnr IS NULL OR trim(Postnr) = '' OR trim(Postnr) = '-'
        THEN NULL 
        ELSE CAST(trim(Postnr) AS INTEGER)
    END AS Postnr,
    
    By,
    Anvendelse,
    Type,
    Undertype,
    
    -- Fix Opførelsesår: Convert to integer
    CASE 
        WHEN "Opførelsesår" IS NULL OR trim("Opførelsesår") = '' OR trim("Opførelsesår") = '-'
        THEN NULL 
        ELSE CAST(trim("Opførelsesår") AS INTEGER)
    END AS "Opførelsesår",
    
    "Energimærke",
    "Er fælleslod",
    "Seneste handelspris",
    "Seneste handelsdato",
    "Offentlig ejendomsværdi (Ny)",
    "Realiseret bebyggelsesprocent",
    Grundareal,
    
    -- Fix Areal_ha: Convert Danish decimal format to proper double
    CASE 
        WHEN Areal_ha IS NULL OR trim(Areal_ha) = '' 
        THEN NULL 
        ELSE CAST(replace(trim(Areal_ha), ',', '.') AS REAL)
    END AS Areal_ha,
    
    Vejareal,
    Fredskovsareal,
    "Fredskovsareal omfang",
    Strandbeskyttelsesareal,
    "Strandbeskyttelse omfang"
    
FROM test123;
