{".class": "MypyFile", "_fullname": "numpy._typing", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.ArrayLike", "kind": "Gdef"}, "DTypeLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like.DTypeLike", "kind": "Gdef"}, "NBitBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing.NBitBase", "name": "NBitBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "numpy._typing.NBitBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._typing", "mro": ["numpy._typing.NBitBase", "builtins.object"], "names": {".class": "SymbolTable", "__init_subclass__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class"], "fullname": "numpy._typing.NBitBase.__init_subclass__", "name": "__init_subclass__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "numpy._typing.NBitBase"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init_subclass__ of NBitBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing.NBitBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NDArray": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.NDArray", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "_128Bit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy._typing._256Bit"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._128Bit", "name": "_128Bit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._typing._128Bit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._typing", "mro": ["numpy._typing._128Bit", "numpy._typing._256Bit", "numpy._typing.NBitBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._128Bit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._128Bit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_16Bit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy._typing._32Bit"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._16Bit", "name": "_16Bit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._typing._16Bit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._typing", "mro": ["numpy._typing._16Bit", "numpy._typing._32Bit", "numpy._typing._64Bit", "numpy._typing._80Bit", "numpy._typing._96Bit", "numpy._typing._128Bit", "numpy._typing._256Bit", "numpy._typing.NBitBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._16Bit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._16Bit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_256Bit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy._typing.NBitBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._256Bit", "name": "_256Bit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._typing._256Bit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._typing", "mro": ["numpy._typing._256Bit", "numpy._typing.NBitBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._256Bit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._256Bit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_32Bit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy._typing._64Bit"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._32Bit", "name": "_32Bit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._typing._32Bit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._typing", "mro": ["numpy._typing._32Bit", "numpy._typing._64Bit", "numpy._typing._80Bit", "numpy._typing._96Bit", "numpy._typing._128Bit", "numpy._typing._256Bit", "numpy._typing.NBitBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._32Bit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._32Bit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_64Bit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy._typing._80Bit"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._64Bit", "name": "_64Bit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._typing._64Bit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._typing", "mro": ["numpy._typing._64Bit", "numpy._typing._80Bit", "numpy._typing._96Bit", "numpy._typing._128Bit", "numpy._typing._256Bit", "numpy._typing.NBitBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._64Bit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._64Bit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_80Bit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy._typing._96Bit"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._80Bit", "name": "_80Bit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._typing._80Bit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._typing", "mro": ["numpy._typing._80Bit", "numpy._typing._96Bit", "numpy._typing._128Bit", "numpy._typing._256Bit", "numpy._typing.NBitBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._80Bit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._80Bit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_8Bit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy._typing._16Bit"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._8Bit", "name": "_8Bit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._typing._8Bit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._typing", "mro": ["numpy._typing._8Bit", "numpy._typing._16Bit", "numpy._typing._32Bit", "numpy._typing._64Bit", "numpy._typing._80Bit", "numpy._typing._96Bit", "numpy._typing._128Bit", "numpy._typing._256Bit", "numpy._typing.NBitBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._8Bit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._8Bit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_96Bit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy._typing._128Bit"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._96Bit", "name": "_96Bit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._typing._96Bit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._typing", "mro": ["numpy._typing._96Bit", "numpy._typing._128Bit", "numpy._typing._256Bit", "numpy._typing.NBitBase", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._96Bit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._96Bit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLike", "kind": "Gdef"}, "_ArrayLikeBool_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeBool_co", "kind": "Gdef"}, "_ArrayLikeBytes_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeBytes_co", "kind": "Gdef"}, "_ArrayLikeComplex_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeComplex_co", "kind": "Gdef"}, "_ArrayLikeDT64_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeDT64_co", "kind": "Gdef"}, "_ArrayLikeFloat_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeFloat_co", "kind": "Gdef"}, "_ArrayLikeInt": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeInt", "kind": "Gdef"}, "_ArrayLikeInt_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeInt_co", "kind": "Gdef"}, "_ArrayLikeNumber_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeNumber_co", "kind": "Gdef"}, "_ArrayLikeObject_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeObject_co", "kind": "Gdef"}, "_ArrayLikeStr_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeStr_co", "kind": "Gdef"}, "_ArrayLikeTD64_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeTD64_co", "kind": "Gdef"}, "_ArrayLikeUInt_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeUInt_co", "kind": "Gdef"}, "_ArrayLikeUnknown": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeUnknown", "kind": "Gdef"}, "_ArrayLikeVoid_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeVoid_co", "kind": "Gdef"}, "_BoolCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._BoolCodes", "kind": "Gdef"}, "_BoolLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._BoolLike_co", "kind": "Gdef"}, "_ByteCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._ByteCodes", "kind": "Gdef"}, "_BytesCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._BytesCodes", "kind": "Gdef"}, "_CDoubleCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._CDoubleCodes", "kind": "Gdef"}, "_CLongDoubleCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._CLongDoubleCodes", "kind": "Gdef"}, "_CSingleCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._CSingleCodes", "kind": "Gdef"}, "_CharLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._CharLike_co", "kind": "Gdef"}, "_Complex128Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Complex128Codes", "kind": "Gdef"}, "_Complex64Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Complex64Codes", "kind": "Gdef"}, "_ComplexLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._ComplexLike_co", "kind": "Gdef"}, "_DT64Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._DT64Codes", "kind": "Gdef"}, "_DTypeLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLike", "kind": "Gdef"}, "_DTypeLikeBool": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLikeBool", "kind": "Gdef"}, "_DTypeLikeBytes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLikeBytes", "kind": "Gdef"}, "_DTypeLikeComplex": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLikeComplex", "kind": "Gdef"}, "_DTypeLikeComplex_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLikeComplex_co", "kind": "Gdef"}, "_DTypeLikeDT64": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLikeDT64", "kind": "Gdef"}, "_DTypeLikeFloat": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLikeFloat", "kind": "Gdef"}, "_DTypeLikeInt": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLikeInt", "kind": "Gdef"}, "_DTypeLikeObject": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLikeObject", "kind": "Gdef"}, "_DTypeLikeStr": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLikeStr", "kind": "Gdef"}, "_DTypeLikeTD64": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLikeTD64", "kind": "Gdef"}, "_DTypeLikeUInt": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLikeUInt", "kind": "Gdef"}, "_DTypeLikeVoid": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLikeVoid", "kind": "Gdef"}, "_DoubleCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._DoubleCodes", "kind": "Gdef"}, "_FiniteNestedSequence": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._FiniteNestedSequence", "kind": "Gdef"}, "_Float16Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Float16Codes", "kind": "Gdef"}, "_Float32Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Float32Codes", "kind": "Gdef"}, "_Float64Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Float64Codes", "kind": "Gdef"}, "_FloatLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._FloatLike_co", "kind": "Gdef"}, "_GUFunc_Nin2_Nout1": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "kind": "Gdef"}, "_HalfCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._HalfCodes", "kind": "Gdef"}, "_Int16Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Int16Codes", "kind": "Gdef"}, "_Int32Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Int32Codes", "kind": "Gdef"}, "_Int64Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Int64Codes", "kind": "Gdef"}, "_Int8Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._Int8Codes", "kind": "Gdef"}, "_IntCCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._IntCCodes", "kind": "Gdef"}, "_IntCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._IntCodes", "kind": "Gdef"}, "_IntLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._IntLike_co", "kind": "Gdef"}, "_IntPCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._IntPCodes", "kind": "Gdef"}, "_LongDoubleCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._LongDoubleCodes", "kind": "Gdef"}, "_LongLongCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._LongLongCodes", "kind": "Gdef"}, "_NBitByte": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit._NBitByte", "kind": "Gdef"}, "_NBitDouble": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit._NBitDouble", "kind": "Gdef"}, "_NBitHalf": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit._NBitHalf", "kind": "Gdef"}, "_NBitInt": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit._NBitInt", "kind": "Gdef"}, "_NBitIntC": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit._NBitIntC", "kind": "Gdef"}, "_NBitIntP": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit._NBitIntP", "kind": "Gdef"}, "_NBitLongDouble": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit._NBitLongDouble", "kind": "Gdef"}, "_NBitLongLong": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit._NBitLongLong", "kind": "Gdef"}, "_NBitShort": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit._NBitShort", "kind": "Gdef"}, "_NBitSingle": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit._NBitSingle", "kind": "Gdef"}, "_NestedSequence": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nested_sequence._NestedSequence", "kind": "Gdef"}, "_NumberLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._NumberLike_co", "kind": "Gdef"}, "_ObjectCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._ObjectCodes", "kind": "Gdef"}, "_ScalarLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._ScalarLike_co", "kind": "Gdef"}, "_Shape": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._shape._Shape", "kind": "Gdef"}, "_ShapeLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._shape._ShapeLike", "kind": "Gdef"}, "_ShortCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._ShortCodes", "kind": "Gdef"}, "_SingleCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._SingleCodes", "kind": "Gdef"}, "_StrCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._StrCodes", "kind": "Gdef"}, "_SupportsArray": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._SupportsArray", "kind": "Gdef"}, "_SupportsArrayFunc": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._SupportsArrayFunc", "kind": "Gdef"}, "_SupportsDType": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._SupportsDType", "kind": "Gdef"}, "_TD64Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._TD64Codes", "kind": "Gdef"}, "_TD64Like_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._TD64Like_co", "kind": "Gdef"}, "_UByteCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UByteCodes", "kind": "Gdef"}, "_UFunc_Nin1_Nout1": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "kind": "Gdef"}, "_UFunc_Nin1_Nout2": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "kind": "Gdef"}, "_UFunc_Nin2_Nout1": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "kind": "Gdef"}, "_UFunc_Nin2_Nout2": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "kind": "Gdef"}, "_UInt16Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UInt16Codes", "kind": "Gdef"}, "_UInt32Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UInt32Codes", "kind": "Gdef"}, "_UInt64Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UInt64Codes", "kind": "Gdef"}, "_UInt8Codes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UInt8Codes", "kind": "Gdef"}, "_UIntCCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UIntCCodes", "kind": "Gdef"}, "_UIntCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UIntCodes", "kind": "Gdef"}, "_UIntLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._UIntLike_co", "kind": "Gdef"}, "_UIntPCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UIntPCodes", "kind": "Gdef"}, "_ULongLongCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._ULongLongCodes", "kind": "Gdef"}, "_UShortCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._UShortCodes", "kind": "Gdef"}, "_UnknownType": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._UnknownType", "kind": "Gdef"}, "_VoidCodes": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._char_codes._VoidCodes", "kind": "Gdef"}, "_VoidDTypeLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._VoidDTypeLike", "kind": "Gdef"}, "_VoidLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._VoidLike_co", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "final": {".class": "SymbolTableNode", "cross_ref": "typing.final", "kind": "Gdef"}, "set_module": {".class": "SymbolTableNode", "cross_ref": "numpy._utils.set_module", "kind": "Gdef"}, "ufunc": {".class": "SymbolTableNode", "cross_ref": "numpy.ufunc", "kind": "Gdef"}}, "path": "c:\\AnacondaPath\\Lib\\site-packages\\numpy\\_typing\\__init__.py"}