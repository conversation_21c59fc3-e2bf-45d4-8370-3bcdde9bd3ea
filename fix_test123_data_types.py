"""
Fix Data Types in test123.gpkg - Standalone Script

This script fixes data type issues in the test123.gpkg file where numeric fields
are incorrectly stored as text due to Danish formatting (comma as decimal separator).

The script will:
1. Load the test123 layer from test123.gpkg
2. Fix data types for specific fields:
   - Longitude: String → Double (handles "10,000048" format)
   - Latitude: String → Double (handles "56,456005" format)
   - Areal_ha: String → Double (handles "1,75" format)
   - Postnr: String → Integer (handles "8940" format)
   - <PERSON><PERSON><PERSON><PERSON> ejer alder: String → Integer
   - Opførelsesår: String → Integer
3. Save the corrected data back to test123.gpkg as a new layer "test123_fixed"
4. Provide validation report

Usage:
    python fix_test123_data_types.py
"""

import os
import sys
from typing import Dict, Any, List

# This script is designed to run within QGIS Python Console
# or as a standalone script with QGIS environment already set up

try:
    from qgis.core import (
        QgsVectorLayer,
        QgsProcessingContext,
        QgsProcessingFeedback,
        QgsProject,
        QgsProcessingException,
        QgsProcessing,
        QVariant,
        QgsProcessingUtils,
    )
    from qgis.PyQt.QtCore import QVariant
    import processing

    print("✅ QGIS modules imported successfully")
except ImportError as e:
    print(f"❌ Error importing QGIS modules: {e}")
    print(
        "Please run this script from within QGIS Python Console or ensure QGIS environment is properly set up."
    )
    sys.exit(1)


class DataTypeFixer:
    """Class to handle data type fixing for Danish numeric data"""

    def __init__(self):
        self.feedback = QgsProcessingFeedback()
        self.context = QgsProcessingContext()

    def fix_data_types(
        self,
        input_gpkg_path: str,
        layer_name: str = "test123",
        output_layer_name: str = "test123_fixed",
    ) -> bool:
        """
        Fix data types in the specified GeoPackage layer

        Args:
            input_gpkg_path: Path to the input GeoPackage file
            layer_name: Name of the layer to process
            output_layer_name: Name for the output layer

        Returns:
            bool: True if successful, False otherwise
        """

        try:
            # Load the input layer
            print(f"Loading layer '{layer_name}' from {input_gpkg_path}")
            input_layer = QgsVectorLayer(
                f"{input_gpkg_path}|layername={layer_name}", layer_name, "ogr"
            )

            if not input_layer.isValid():
                print(f"❌ Failed to load layer '{layer_name}' from {input_gpkg_path}")
                return False

            print(f"✅ Loaded layer with {input_layer.featureCount():,} features")

            # Analyze current field types
            self._analyze_current_fields(input_layer)

            # Create field mappings
            field_mappings = self._create_field_mappings(input_layer)

            # Prepare output path
            output_path = f"{input_gpkg_path}|layername={output_layer_name}"

            # Apply field refactoring
            print("\n🔧 Applying data type fixes...")
            refactor_params = {
                "INPUT": input_layer,
                "FIELDS_MAPPING": field_mappings,
                "OUTPUT": output_path,
            }

            result = processing.run(
                "native:refactorfields",
                refactor_params,
                context=self.context,
                feedback=self.feedback,
            )

            if result and "OUTPUT" in result:
                print(f"✅ Data type fixes completed successfully")
                print(
                    f"📁 Output saved as layer '{output_layer_name}' in {input_gpkg_path}"
                )

                # Validate the results
                self._validate_results(input_gpkg_path, output_layer_name)
                return True
            else:
                print("❌ Failed to create output")
                return False

        except Exception as e:
            print(f"❌ Error during processing: {str(e)}")
            return False

    def _analyze_current_fields(self, layer: QgsVectorLayer):
        """Analyze current field types and identify issues"""

        print(f"\n📊 Current field analysis for layer '{layer.name()}':")
        print("-" * 60)

        problematic_fields = []

        for field in layer.fields():
            field_name = field.name()
            field_type = QVariant.typeToName(field.type())

            # Check if this field should have a different type
            expected_type = self._get_expected_type(field_name)
            if expected_type and expected_type != field_type:
                problematic_fields.append(
                    {
                        "name": field_name,
                        "current": field_type,
                        "expected": expected_type,
                    }
                )
                print(f"  ⚠️  {field_name}: {field_type} → should be {expected_type}")
            else:
                print(f"  ✅  {field_name}: {field_type}")

        if problematic_fields:
            print(f"\n🔍 Found {len(problematic_fields)} fields with data type issues")
        else:
            print(f"\n✅ No data type issues detected")

    def _get_expected_type(self, field_name: str) -> str:
        """Get expected data type for a field"""

        danish_numeric_fields = ["Longitude", "Latitude", "Areal_ha"]
        integer_fields = ["Postnr", "Primær ejer alder", "Opførelsesår"]

        if field_name in danish_numeric_fields:
            return "double"
        elif field_name in integer_fields:
            return "int"
        else:
            return None

    def _create_field_mappings(
        self, input_layer: QgsVectorLayer
    ) -> List[Dict[str, Any]]:
        """Create field mappings with proper data type conversions"""

        field_mappings = []

        # Fields that need special handling for Danish numeric format
        danish_numeric_fields = {
            "Longitude": {"type": QVariant.Double, "precision": 6},
            "Latitude": {"type": QVariant.Double, "precision": 6},
            "Areal_ha": {"type": QVariant.Double, "precision": 2},
        }

        # Fields that should be integers
        integer_fields = {
            "Postnr": {"type": QVariant.Int, "precision": 0},
            "Primær ejer alder": {"type": QVariant.Int, "precision": 0},
            "Opførelsesår": {"type": QVariant.Int, "precision": 0},
        }

        print(f"\n🔧 Creating field mappings:")

        # Process each field in the input layer
        for field in input_layer.fields():
            field_name = field.name()

            if field_name in danish_numeric_fields:
                # Handle Danish numeric format (comma as decimal separator)
                config = danish_numeric_fields[field_name]
                expression = f"""
                CASE 
                    WHEN "{field_name}" IS NULL OR trim("{field_name}") = '' 
                    THEN NULL
                    ELSE to_real(replace(trim("{field_name}"), ',', '.'))
                END
                """
                print(f"  🔄 {field_name}: String → Double (Danish format)")

            elif field_name in integer_fields:
                # Handle integer conversion
                config = integer_fields[field_name]
                expression = f"""
                CASE 
                    WHEN "{field_name}" IS NULL OR trim("{field_name}") = '' OR trim("{field_name}") = '-'
                    THEN NULL
                    ELSE to_int(trim("{field_name}"))
                END
                """
                print(f"  🔄 {field_name}: String → Integer")

            else:
                # Keep existing field as-is
                config = {"type": field.type(), "precision": field.precision()}
                expression = f'"{field_name}"'
                print(
                    f"  ✅ {field_name}: Keeping as {QVariant.typeToName(field.type())}"
                )

            # Create field mapping
            field_mapping = {
                "name": field_name,
                "type": config["type"],
                "length": field.length(),
                "precision": config["precision"],
                "expression": expression,
                "alias": "",
                "comment": "",
                "sub_type": 0,
                "type_name": QVariant.typeToName(config["type"]),
            }

            field_mappings.append(field_mapping)

        return field_mappings

    def _validate_results(self, gpkg_path: str, layer_name: str):
        """Validate the results of the data type fixing"""

        print(f"\n🔍 Validating results in layer '{layer_name}':")
        print("-" * 50)

        try:
            # Load the output layer
            output_layer = QgsVectorLayer(
                f"{gpkg_path}|layername={layer_name}", layer_name, "ogr"
            )

            if not output_layer.isValid():
                print(f"❌ Could not load output layer for validation")
                return

            print(
                f"✅ Output layer loaded with {output_layer.featureCount():,} features"
            )

            # Check field types
            target_fields = {
                "Longitude": "double",
                "Latitude": "double",
                "Areal_ha": "double",
                "Postnr": "int",
                "Primær ejer alder": "int",
                "Opførelsesår": "int",
            }

            all_correct = True
            for field in output_layer.fields():
                field_name = field.name()
                if field_name in target_fields:
                    actual_type = QVariant.typeToName(field.type()).lower()
                    expected_type = target_fields[field_name]

                    if actual_type == expected_type:
                        print(f"  ✅ {field_name}: {actual_type}")
                    else:
                        print(
                            f"  ❌ {field_name}: {actual_type} (expected {expected_type})"
                        )
                        all_correct = False

            if all_correct:
                print(f"\n🎉 All target fields have correct data types!")
            else:
                print(f"\n⚠️  Some fields still have incorrect data types")

        except Exception as e:
            print(f"❌ Error during validation: {str(e)}")


def main():
    """Main function to run the data type fixer"""

    print("=" * 70)
    print("🔧 DATA TYPE FIXER FOR test123.gpkg")
    print("=" * 70)

    # Initialize QGIS Application
    QgsApplication.setPrefixPath("C:/Program Files/QGIS 3.34.0/apps/qgis", True)
    qgs = QgsApplication([], False)
    qgs.initQgis()

    try:
        # Initialize processing
        import processing
        from processing.core.Processing import Processing

        Processing.initialize()

        # Set up file paths
        current_dir = os.getcwd()
        gpkg_path = os.path.join(current_dir, "test123.gpkg")

        if not os.path.exists(gpkg_path):
            print(f"❌ File not found: {gpkg_path}")
            print(f"   Current directory: {current_dir}")
            print(f"   Please ensure test123.gpkg is in the current directory")
            return False

        print(f"📁 Input file: {gpkg_path}")

        # Create fixer instance and run
        fixer = DataTypeFixer()
        success = fixer.fix_data_types(gpkg_path, "test123", "test123_fixed")

        if success:
            print(f"\n🎉 Data type fixing completed successfully!")
            print(f"📊 Original data: test123 layer")
            print(f"📊 Fixed data: test123_fixed layer")
            print(f"📁 Both layers are now available in {gpkg_path}")
        else:
            print(f"\n❌ Data type fixing failed")

        return success

    finally:
        # Clean up QGIS
        qgs.exitQgis()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
