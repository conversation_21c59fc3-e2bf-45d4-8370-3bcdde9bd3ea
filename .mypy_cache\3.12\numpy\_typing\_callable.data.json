{".class": "MypyFile", "_fullname": "numpy._typing._callable", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NBitBase": {".class": "SymbolTableNode", "cross_ref": "numpy._typing.NBitBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NDArray": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.NDArray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_2Tuple": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T1", "id": 1, "name": "_T1", "namespace": "numpy._typing._callable._2Tuple", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "numpy._typing._callable._2Tuple", "line": 53, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T1", "id": 1, "name": "_T1", "namespace": "numpy._typing._callable._2Tuple", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T1", "id": 1, "name": "_T1", "namespace": "numpy._typing._callable._2Tuple", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_BoolBitOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._callable._BoolBitOp", "name": "_BoolBitOp", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolBitOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy._typing._callable._BoolBitOp", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._callable", "mro": ["numpy._typing._callable._BoolBitOp", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._callable._BoolBitOp.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolBitOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolBitOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolBitOp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._BoolLike_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolBitOp", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolBitOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolBitOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolBitOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolBitOp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._BoolLike_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolBitOp", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolBitOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolBitOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolBitOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolBitOp"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolBitOp", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolBitOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolBitOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolBitOp"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolBitOp", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolBitOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolBitOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolBitOp"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolBitOp.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolBitOp", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolBitOp.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolBitOp.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolBitOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolBitOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolBitOp"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolBitOp.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolBitOp", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolBitOp.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolBitOp.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolBitOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolBitOp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._BoolLike_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolBitOp", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolBitOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolBitOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolBitOp"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolBitOp", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolBitOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolBitOp"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolBitOp.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolBitOp", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolBitOp.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolBitOp.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._BoolBitOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolBitOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolBitOp"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_GenericType_co"], "typeddict_type": null}}, "_BoolDivMod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._callable._BoolDivMod", "name": "_BoolDivMod", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy._typing._callable._BoolDivMod", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._callable", "mro": ["numpy._typing._callable._BoolDivMod", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._callable._BoolDivMod.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolDivMod", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._BoolLike_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolDivMod", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._BoolLike_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolDivMod", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolDivMod", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolDivMod", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy._typing._callable._BoolDivMod.__call__#2", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy._typing._callable._BoolDivMod.__call__#2", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolDivMod", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy._typing._callable._BoolDivMod.__call__#2", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy._typing._callable._BoolDivMod.__call__#2", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolDivMod", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolDivMod.__call__#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolDivMod.__call__#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolDivMod.__call__#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolDivMod", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolDivMod.__call__#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolDivMod.__call__#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolDivMod.__call__#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolDivMod", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._FloatType", "id": -1, "name": "_FloatType", "namespace": "numpy._typing._callable._BoolDivMod.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.floating"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._FloatType", "id": -1, "name": "_FloatType", "namespace": "numpy._typing._callable._BoolDivMod.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.floating"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._FloatType", "id": -1, "name": "_FloatType", "namespace": "numpy._typing._callable._BoolDivMod.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.floating"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolDivMod", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._FloatType", "id": -1, "name": "_FloatType", "namespace": "numpy._typing._callable._BoolDivMod.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.floating"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._FloatType", "id": -1, "name": "_FloatType", "namespace": "numpy._typing._callable._BoolDivMod.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.floating"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._FloatType", "id": -1, "name": "_FloatType", "namespace": "numpy._typing._callable._BoolDivMod.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.floating"}, "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolDivMod", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._BoolLike_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolDivMod", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolDivMod", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy._typing._callable._BoolDivMod.__call__#2", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy._typing._callable._BoolDivMod.__call__#2", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolDivMod", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolDivMod.__call__#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolDivMod.__call__#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolDivMod.__call__#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolDivMod", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._FloatType", "id": -1, "name": "_FloatType", "namespace": "numpy._typing._callable._BoolDivMod.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.floating"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._FloatType", "id": -1, "name": "_FloatType", "namespace": "numpy._typing._callable._BoolDivMod.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.floating"}, "values": [], "variance": 0}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._FloatType", "id": -1, "name": "_FloatType", "namespace": "numpy._typing._callable._BoolDivMod.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.floating"}, "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._BoolDivMod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._callable._BoolDivMod", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BoolLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._BoolLike_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_BoolMod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._callable._BoolMod", "name": "_Bool<PERSON>od", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy._typing._callable._BoolMod", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._callable", "mro": ["numpy._typing._callable._BoolMod", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._callable._BoolMod.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolMod", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._BoolLike_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolMod", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._BoolLike_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolMod", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolMod", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolMod", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolMod", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolMod", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolMod.__call__#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolMod.__call__#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolMod.__call__#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolMod", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolMod.__call__#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolMod.__call__#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolMod.__call__#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolMod", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._FloatType", "id": -1, "name": "_FloatType", "namespace": "numpy._typing._callable._BoolMod.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.floating"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._FloatType", "id": -1, "name": "_FloatType", "namespace": "numpy._typing._callable._BoolMod.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.floating"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._FloatType", "id": -1, "name": "_FloatType", "namespace": "numpy._typing._callable._BoolMod.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.floating"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolMod", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._FloatType", "id": -1, "name": "_FloatType", "namespace": "numpy._typing._callable._BoolMod.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.floating"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._FloatType", "id": -1, "name": "_FloatType", "namespace": "numpy._typing._callable._BoolMod.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.floating"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._FloatType", "id": -1, "name": "_FloatType", "namespace": "numpy._typing._callable._BoolMod.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.floating"}, "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolMod", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._BoolLike_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int8"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolMod", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolMod", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolMod", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolMod.__call__#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolMod.__call__#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "id": -1, "name": "_IntType", "namespace": "numpy._typing._callable._BoolMod.__call__#3", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolMod", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._FloatType", "id": -1, "name": "_FloatType", "namespace": "numpy._typing._callable._BoolMod.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.floating"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._FloatType", "id": -1, "name": "_FloatType", "namespace": "numpy._typing._callable._BoolMod.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.floating"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._FloatType", "id": -1, "name": "_FloatType", "namespace": "numpy._typing._callable._BoolMod.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.floating"}, "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._BoolMod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._callable._BoolMod", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BoolOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._callable._BoolOp", "name": "_BoolOp", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy._typing._callable._BoolOp", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._callable", "mro": ["numpy._typing._callable._BoolOp", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._callable._BoolOp.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolOp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._BoolLike_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON>p", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolOp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._BoolLike_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON>p", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolOp"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON>p", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolOp"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON>p", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolOp"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON>p", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolOp"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON>p", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolOp"}, "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON>p", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex128"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolOp"}, "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON>p", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex128"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolOp"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolOp.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON>p", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolOp.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolOp.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolOp"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolOp.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON>p", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolOp.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolOp.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolOp"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._BoolLike_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON>p", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolOp"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON>p", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolOp"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON>p", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolOp"}, "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON>p", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex128"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolOp"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolOp.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON>p", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolOp.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolOp.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._BoolOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "id": 1, "name": "_GenericType_co", "namespace": "numpy._typing._callable._BoolOp", "upper_bound": "numpy.generic", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._BoolOp"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_GenericType_co"], "typeddict_type": null}}, "_BoolSub": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._callable._BoolSub", "name": "_BoolSub", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy._typing._callable._BoolSub", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._callable", "mro": ["numpy._typing._callable._BoolSub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._callable._BoolSub.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolSub.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolSub", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolSub.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolSub", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolSub.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolSub", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolSub.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolSub", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolSub.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolSub", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolSub.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolSub", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolSub.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolSub", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex128"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolSub.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolSub", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex128"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolSub.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolSub", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolSub.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolSub.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolSub.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolSub.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolSub", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolSub.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolSub.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolSub.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolSub", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolSub", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolSub", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolSub", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex128"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolSub", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolSub.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolSub.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolSub.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._BoolSub.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._callable._BoolSub", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BoolTrueDiv": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._callable._BoolTrueDiv", "name": "_BoolTrueDiv", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy._typing._callable._BoolTrueDiv", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._callable", "mro": ["numpy._typing._callable._BoolTrueDiv", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._callable._BoolTrueDiv.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolTrueDiv.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolTrueDiv", {".class": "UnionType", "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._IntLike_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolTrueDiv", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolTrueDiv.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolTrueDiv", {".class": "UnionType", "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._IntLike_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolTrueDiv", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolTrueDiv.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolTrueDiv", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolTrueDiv", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex128"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolTrueDiv.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolTrueDiv", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolTrueDiv", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex128"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._BoolTrueDiv.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolTrueDiv", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolTrueDiv.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolTrueDiv", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolTrueDiv.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolTrueDiv.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._BoolTrueDiv.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolTrueDiv", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolTrueDiv.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolTrueDiv", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolTrueDiv.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolTrueDiv.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolTrueDiv", {".class": "UnionType", "items": ["builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._IntLike_co"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolTrueDiv", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolTrueDiv", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolTrueDiv", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex128"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._BoolTrueDiv", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolTrueDiv.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BoolTrueDiv", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolTrueDiv.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "id": -1, "name": "_NumberType", "namespace": "numpy._typing._callable._BoolTrueDiv.__call__", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._BoolTrueDiv.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._callable._BoolTrueDiv", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ComparisonOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._callable._ComparisonOp", "name": "_ComparisonOp", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T1_contra", "id": 1, "name": "_T1_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T2_contra", "id": 2, "name": "_T2_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy._typing._callable._ComparisonOp", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._callable", "mro": ["numpy._typing._callable._ComparisonOp", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._callable._ComparisonOp.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._ComparisonOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T1_contra", "id": 1, "name": "_T1_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T2_contra", "id": 2, "name": "_T2_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "numpy._typing._callable._ComparisonOp"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T1_contra", "id": 1, "name": "_T1_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ComparisonOp", "ret_type": "numpy.bool_", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._ComparisonOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T1_contra", "id": 1, "name": "_T1_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T2_contra", "id": 2, "name": "_T2_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "numpy._typing._callable._ComparisonOp"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T1_contra", "id": 1, "name": "_T1_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ComparisonOp", "ret_type": "numpy.bool_", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._ComparisonOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T1_contra", "id": 1, "name": "_T1_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T2_contra", "id": 2, "name": "_T2_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "numpy._typing._callable._ComparisonOp"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T2_contra", "id": 2, "name": "_T2_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ComparisonOp", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._ComparisonOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T1_contra", "id": 1, "name": "_T1_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T2_contra", "id": 2, "name": "_T2_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "numpy._typing._callable._ComparisonOp"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T2_contra", "id": 2, "name": "_T2_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ComparisonOp", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._ComparisonOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T1_contra", "id": 1, "name": "_T1_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T2_contra", "id": 2, "name": "_T2_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "numpy._typing._callable._ComparisonOp"}, {".class": "UnionType", "items": ["numpy._typing._callable._SupportsLT", "numpy._typing._callable._SupportsGT", {".class": "Instance", "args": [{".class": "UnionType", "items": ["numpy._typing._callable._SupportsLT", "numpy._typing._callable._SupportsGT"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ComparisonOp", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._ComparisonOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T1_contra", "id": 1, "name": "_T1_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T2_contra", "id": 2, "name": "_T2_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "numpy._typing._callable._ComparisonOp"}, {".class": "UnionType", "items": ["numpy._typing._callable._SupportsLT", "numpy._typing._callable._SupportsGT", {".class": "Instance", "args": [{".class": "UnionType", "items": ["numpy._typing._callable._SupportsLT", "numpy._typing._callable._SupportsGT"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ComparisonOp", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T1_contra", "id": 1, "name": "_T1_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T2_contra", "id": 2, "name": "_T2_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "numpy._typing._callable._ComparisonOp"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T1_contra", "id": 1, "name": "_T1_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ComparisonOp", "ret_type": "numpy.bool_", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T1_contra", "id": 1, "name": "_T1_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T2_contra", "id": 2, "name": "_T2_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "numpy._typing._callable._ComparisonOp"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T2_contra", "id": 2, "name": "_T2_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ComparisonOp", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T1_contra", "id": 1, "name": "_T1_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T2_contra", "id": 2, "name": "_T2_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "numpy._typing._callable._ComparisonOp"}, {".class": "UnionType", "items": ["numpy._typing._callable._SupportsLT", "numpy._typing._callable._SupportsGT", {".class": "Instance", "args": [{".class": "UnionType", "items": ["numpy._typing._callable._SupportsLT", "numpy._typing._callable._SupportsGT"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy._typing._nested_sequence._NestedSequence"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ComparisonOp", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._ComparisonOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T1_contra", "id": 1, "name": "_T1_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T2_contra", "id": 2, "name": "_T2_contra", "namespace": "numpy._typing._callable._ComparisonOp", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "numpy._typing._callable._ComparisonOp"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_T1_contra", "_T2_contra"], "typeddict_type": null}}, "_ComplexOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._callable._ComplexOp", "name": "_ComplexOp", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy._typing._callable._ComplexOp", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._callable", "mro": ["numpy._typing._callable._ComplexOp", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._callable._ComplexOp.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._ComplexOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._ComplexOp"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ComplexOp", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._ComplexOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._ComplexOp"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ComplexOp", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._ComplexOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._ComplexOp"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ComplexOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._ComplexOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._ComplexOp"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ComplexOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._ComplexOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._ComplexOp"}, "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ComplexOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._ComplexOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._ComplexOp"}, "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ComplexOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._ComplexOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._ComplexOp"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._ComplexOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._ComplexOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._ComplexOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._ComplexOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ComplexOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._ComplexOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._ComplexOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._ComplexOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._ComplexOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._ComplexOp"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._ComplexOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._ComplexOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._ComplexOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._ComplexOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ComplexOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._ComplexOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._ComplexOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._ComplexOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._ComplexOp"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ComplexOp", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._ComplexOp"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ComplexOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._ComplexOp"}, "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ComplexOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._ComplexOp"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._ComplexOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._ComplexOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._ComplexOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._ComplexOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ComplexOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._ComplexOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._ComplexOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._ComplexOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._ComplexOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._ComplexOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._ComplexOp"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_NBit1"], "typeddict_type": null}}, "_FloatDivMod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._callable._FloatDivMod", "name": "_FloatDivMod", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy._typing._callable._FloatDivMod", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._callable", "mro": ["numpy._typing._callable._FloatDivMod", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._callable._FloatDivMod.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._FloatDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatDivMod"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._FloatDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatDivMod"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._FloatDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatDivMod"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._FloatDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatDivMod"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._FloatDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatDivMod"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._FloatDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatDivMod"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._FloatDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatDivMod"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._FloatDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatDivMod"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatDivMod"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatDivMod"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatDivMod"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatDivMod"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._FloatDivMod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatDivMod"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_NBit1"], "typeddict_type": null}}, "_FloatLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._FloatLike_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FloatMod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._callable._FloatMod", "name": "_FloatMod", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy._typing._callable._FloatMod", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._callable", "mro": ["numpy._typing._callable._FloatMod", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._callable._FloatMod.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._FloatMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatMod"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatMod", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._FloatMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatMod"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatMod", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._FloatMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatMod"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatMod", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._FloatMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatMod"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatMod", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._FloatMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatMod"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatMod", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._FloatMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatMod"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatMod", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._FloatMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatMod"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatMod", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._FloatMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatMod"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatMod", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatMod"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatMod", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatMod"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatMod", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatMod"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatMod", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatMod"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatMod", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._FloatMod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatMod"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_NBit1"], "typeddict_type": null}}, "_FloatOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._callable._FloatOp", "name": "_FloatOp", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy._typing._callable._FloatOp", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._callable", "mro": ["numpy._typing._callable._FloatOp", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._callable._FloatOp.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._FloatOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatOp"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatOp", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._FloatOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatOp"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatOp", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._FloatOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatOp"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._FloatOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatOp"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._FloatOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatOp"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._FloatOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatOp"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._FloatOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatOp"}, "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._FloatOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatOp"}, "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._FloatOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatOp"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._FloatOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatOp"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatOp"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatOp", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatOp"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatOp"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatOp"}, "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatOp"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _FloatOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._FloatOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._FloatOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._FloatOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._FloatOp"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_NBit1"], "typeddict_type": null}}, "_FloatType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._FloatType", "name": "_FloatType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.floating"}, "values": [], "variance": 0}}, "_GenericType_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._GenericType_co", "name": "_GenericType_co", "upper_bound": "numpy.generic", "values": [], "variance": 1}}, "_IntLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._IntLike_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_IntTrueDiv": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._callable._IntTrueDiv", "name": "_IntTrueDiv", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy._typing._callable._IntTrueDiv", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._callable", "mro": ["numpy._typing._callable._IntTrueDiv", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._callable._IntTrueDiv.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._IntTrueDiv.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._IntTrueDiv"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _IntTrueDiv", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._IntTrueDiv.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._IntTrueDiv"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _IntTrueDiv", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._IntTrueDiv.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._IntTrueDiv"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _IntTrueDiv", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._IntTrueDiv.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._IntTrueDiv"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _IntTrueDiv", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._IntTrueDiv.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._IntTrueDiv"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _IntTrueDiv", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._IntTrueDiv.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._IntTrueDiv"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _IntTrueDiv", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._IntTrueDiv.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._IntTrueDiv"}, "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _IntTrueDiv", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._IntTrueDiv.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._IntTrueDiv"}, "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _IntTrueDiv", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._IntTrueDiv.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._IntTrueDiv"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._IntTrueDiv.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.integer"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _IntTrueDiv", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._IntTrueDiv.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._IntTrueDiv.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._IntTrueDiv.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._IntTrueDiv"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._IntTrueDiv.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.integer"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _IntTrueDiv", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._IntTrueDiv.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._IntTrueDiv.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._IntTrueDiv"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _IntTrueDiv", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._IntTrueDiv"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _IntTrueDiv", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._IntTrueDiv"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _IntTrueDiv", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._IntTrueDiv"}, "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _IntTrueDiv", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._IntTrueDiv"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._IntTrueDiv.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.integer"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _IntTrueDiv", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._IntTrueDiv.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._IntTrueDiv.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntTrueDiv.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._IntTrueDiv", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._IntTrueDiv"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_NBit1"], "typeddict_type": null}}, "_IntType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._IntType", "name": "_IntType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.integer"}, "values": [], "variance": 0}}, "_NBit1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "name": "_NBit1", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}}, "_NBit2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "name": "_NBit2", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}}, "_NBitDouble": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit._NBitDouble", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_NBitInt": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nbit._NBitInt", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_NestedSequence": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._nested_sequence._NestedSequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_NumberLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._NumberLike_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_NumberOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._callable._NumberOp", "name": "_NumberOp", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy._typing._callable._NumberOp", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._callable", "mro": ["numpy._typing._callable._NumberOp", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy._typing._callable._NumberOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._NumberOp", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._NumberLike_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _NumberOp", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._callable._NumberOp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_NumberType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType", "name": "_NumberType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 0}}, "_NumberType_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType_co", "name": "_NumberType_co", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 1}}, "_SignedIntBitOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._callable._SignedIntBitOp", "name": "_SignedIntBitOp", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy._typing._callable._SignedIntBitOp", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._callable", "mro": ["numpy._typing._callable._SignedIntBitOp", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._callable._SignedIntBitOp.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._SignedIntBitOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntBitOp"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntBitOp", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._SignedIntBitOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntBitOp"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntBitOp", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._SignedIntBitOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntBitOp"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntBitOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._SignedIntBitOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntBitOp"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntBitOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._SignedIntBitOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntBitOp"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntBitOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntBitOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntBitOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntBitOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._SignedIntBitOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntBitOp"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntBitOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntBitOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntBitOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntBitOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntBitOp"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntBitOp", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntBitOp"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntBitOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntBitOp"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntBitOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntBitOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntBitOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntBitOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._SignedIntBitOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntBitOp"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_NBit1"], "typeddict_type": null}}, "_SignedIntDivMod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._callable._SignedIntDivMod", "name": "_SignedIntDivMod", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy._typing._callable._SignedIntDivMod", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._callable", "mro": ["numpy._typing._callable._SignedIntDivMod", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._callable._SignedIntDivMod.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._SignedIntDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntDivMod"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._SignedIntDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntDivMod"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._SignedIntDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntDivMod"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._SignedIntDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntDivMod"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._SignedIntDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntDivMod"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._SignedIntDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntDivMod"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._SignedIntDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntDivMod"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._SignedIntDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntDivMod"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntDivMod"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntDivMod"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntDivMod"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntDivMod"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._SignedIntDivMod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntDivMod"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_NBit1"], "typeddict_type": null}}, "_SignedIntMod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._callable._SignedIntMod", "name": "_SignedIntMod", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy._typing._callable._SignedIntMod", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._callable", "mro": ["numpy._typing._callable._SignedIntMod", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._callable._SignedIntMod.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._SignedIntMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntMod"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntMod", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._SignedIntMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntMod"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntMod", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._SignedIntMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntMod"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntMod", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._SignedIntMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntMod"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntMod", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._SignedIntMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntMod"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntMod", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._SignedIntMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntMod"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntMod", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._SignedIntMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntMod"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntMod", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._SignedIntMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntMod"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntMod", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntMod"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntMod", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntMod"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntMod", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntMod"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntMod", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntMod"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntMod", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._SignedIntMod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntMod"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_NBit1"], "typeddict_type": null}}, "_SignedIntOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._callable._SignedIntOp", "name": "_SignedIntOp", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy._typing._callable._SignedIntOp", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._callable", "mro": ["numpy._typing._callable._SignedIntOp", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._callable._SignedIntOp.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._SignedIntOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntOp"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._SignedIntOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntOp"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._SignedIntOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntOp"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._SignedIntOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntOp"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._SignedIntOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntOp"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._SignedIntOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntOp"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._SignedIntOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntOp"}, "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._SignedIntOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntOp"}, "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._SignedIntOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntOp"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._SignedIntOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntOp"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntOp"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntOp"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitInt"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntOp"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntOp"}, "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntOp"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._SignedIntOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._SignedIntOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._SignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._SignedIntOp"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_NBit1"], "typeddict_type": null}}, "_SupportsGT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._callable._SupportsGT", "name": "_SupportsGT", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy._typing._callable._SupportsGT", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._callable", "mro": ["numpy._typing._callable._SupportsGT", "builtins.object"], "names": {".class": "SymbolTable", "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy._typing._callable._SupportsGT.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._SupportsGT", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of _SupportsGT", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._SupportsGT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._callable._SupportsGT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SupportsLT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._callable._SupportsLT", "name": "_SupportsLT", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy._typing._callable._SupportsLT", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._callable", "mro": ["numpy._typing._callable._SupportsLT", "builtins.object"], "names": {".class": "SymbolTable", "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy._typing._callable._SupportsLT.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["numpy._typing._callable._SupportsLT", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of _SupportsLT", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._SupportsLT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._callable._SupportsLT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_T1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T1", "name": "_T1", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_T1_contra": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T1_contra", "name": "_T1_contra", "upper_bound": "builtins.object", "values": [], "variance": 2}}, "_T2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T2", "name": "_T2", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_T2_contra": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._T2_contra", "name": "_T2_contra", "upper_bound": "builtins.object", "values": [], "variance": 2}}, "_TD64Div": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._callable._TD64Div", "name": "_TD64Div", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType_co", "id": 1, "name": "_NumberType_co", "namespace": "numpy._typing._callable._TD64Div", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy._typing._callable._TD64Div", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._callable", "mro": ["numpy._typing._callable._TD64Div", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._callable._TD64Div.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._TD64Div.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType_co", "id": 1, "name": "_NumberType_co", "namespace": "numpy._typing._callable._TD64Div", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._TD64Div"}, "numpy.timedelta64"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _TD64Div", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType_co", "id": 1, "name": "_NumberType_co", "namespace": "numpy._typing._callable._TD64Div", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._TD64Div.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType_co", "id": 1, "name": "_NumberType_co", "namespace": "numpy._typing._callable._TD64Div", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._TD64Div"}, "numpy.timedelta64"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _TD64Div", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType_co", "id": 1, "name": "_NumberType_co", "namespace": "numpy._typing._callable._TD64Div", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._TD64Div.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType_co", "id": 1, "name": "_NumberType_co", "namespace": "numpy._typing._callable._TD64Div", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._TD64Div"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._BoolLike_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _TD64Div", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._TD64Div.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType_co", "id": 1, "name": "_NumberType_co", "namespace": "numpy._typing._callable._TD64Div", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._TD64Div"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._BoolLike_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _TD64Div", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._TD64Div.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType_co", "id": 1, "name": "_NumberType_co", "namespace": "numpy._typing._callable._TD64Div", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._TD64Div"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _TD64Div", "ret_type": "numpy.timedelta64", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._TD64Div.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType_co", "id": 1, "name": "_NumberType_co", "namespace": "numpy._typing._callable._TD64Div", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._TD64Div"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _TD64Div", "ret_type": "numpy.timedelta64", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType_co", "id": 1, "name": "_NumberType_co", "namespace": "numpy._typing._callable._TD64Div", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._TD64Div"}, "numpy.timedelta64"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _TD64Div", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType_co", "id": 1, "name": "_NumberType_co", "namespace": "numpy._typing._callable._TD64Div", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType_co", "id": 1, "name": "_NumberType_co", "namespace": "numpy._typing._callable._TD64Div", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._TD64Div"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._BoolLike_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _TD64Div", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType_co", "id": 1, "name": "_NumberType_co", "namespace": "numpy._typing._callable._TD64Div", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._TD64Div"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._FloatLike_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _TD64Div", "ret_type": "numpy.timedelta64", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._TD64Div.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NumberType_co", "id": 1, "name": "_NumberType_co", "namespace": "numpy._typing._callable._TD64Div", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.number"}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy._typing._callable._TD64Div"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_NumberType_co"], "typeddict_type": null}}, "_UnsignedIntBitOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._callable._UnsignedIntBitOp", "name": "_UnsignedIntBitOp", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy._typing._callable._UnsignedIntBitOp", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._callable", "mro": ["numpy._typing._callable._UnsignedIntBitOp", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._callable._UnsignedIntBitOp.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._UnsignedIntBitOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntBitOp"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntBitOp", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._UnsignedIntBitOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntBitOp"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntBitOp", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._UnsignedIntBitOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntBitOp"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntBitOp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._UnsignedIntBitOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntBitOp"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntBitOp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._UnsignedIntBitOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntBitOp"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntBitOp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._UnsignedIntBitOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntBitOp"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntBitOp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._UnsignedIntBitOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntBitOp"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntBitOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntBitOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntBitOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntBitOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._UnsignedIntBitOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntBitOp"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntBitOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntBitOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntBitOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntBitOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntBitOp"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntBitOp", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntBitOp"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntBitOp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntBitOp"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntBitOp", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntBitOp"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntBitOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntBitOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntBitOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntBitOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._UnsignedIntBitOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntBitOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntBitOp"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_NBit1"], "typeddict_type": null}}, "_UnsignedIntDivMod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._callable._UnsignedIntDivMod", "name": "_UnsignedIntDivMod", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy._typing._callable._UnsignedIntDivMod", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._callable", "mro": ["numpy._typing._callable._UnsignedIntDivMod", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._callable._UnsignedIntDivMod.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._UnsignedIntDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntDivMod"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._UnsignedIntDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntDivMod"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._UnsignedIntDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntDivMod"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._UnsignedIntDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntDivMod"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._UnsignedIntDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntDivMod"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._UnsignedIntDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntDivMod"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._UnsignedIntDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntDivMod"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._UnsignedIntDivMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntDivMod"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntDivMod"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntDivMod"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntDivMod"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntDivMod"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntDivMod", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}], "type_ref": "numpy._typing._callable._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntDivMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._UnsignedIntDivMod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntDivMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntDivMod"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_NBit1"], "typeddict_type": null}}, "_UnsignedIntMod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._callable._UnsignedIntMod", "name": "_UnsignedIntMod", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy._typing._callable._UnsignedIntMod", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._callable", "mro": ["numpy._typing._callable._UnsignedIntMod", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._callable._UnsignedIntMod.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._UnsignedIntMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntMod"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntMod", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._UnsignedIntMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntMod"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntMod", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._UnsignedIntMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntMod"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntMod", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._UnsignedIntMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntMod"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntMod", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._UnsignedIntMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntMod"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntMod", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._UnsignedIntMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntMod"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntMod", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._UnsignedIntMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntMod"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntMod", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._UnsignedIntMod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntMod"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntMod", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntMod"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntMod", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntMod"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntMod", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntMod"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntMod", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntMod"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntMod", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntMod.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._UnsignedIntMod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntMod", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntMod"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_NBit1"], "typeddict_type": null}}, "_UnsignedIntOp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._callable._UnsignedIntOp", "name": "_UnsignedIntOp", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy._typing._callable._UnsignedIntOp", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._callable", "mro": ["numpy._typing._callable._UnsignedIntOp", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._callable._UnsignedIntOp.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._UnsignedIntOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntOp"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._UnsignedIntOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntOp"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._UnsignedIntOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntOp"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntOp", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._UnsignedIntOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntOp"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntOp", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._UnsignedIntOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntOp"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._UnsignedIntOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntOp"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._UnsignedIntOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntOp"}, "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._UnsignedIntOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntOp"}, "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._callable._UnsignedIntOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntOp"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._callable._UnsignedIntOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntOp"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntOp"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntOp"}, {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.<PERSON><PERSON><PERSON>r"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntOp", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntOp"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntOp"}, "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._nbit._NBitDouble"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntOp"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UnsignedIntOp", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.unsignedinteger"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit2", "id": -1, "name": "_NBit2", "namespace": "numpy._typing._callable._UnsignedIntOp.__call__", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._UnsignedIntOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._callable._NBit1", "id": 1, "name": "_NBit1", "namespace": "numpy._typing._callable._UnsignedIntOp", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._callable._UnsignedIntOp"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_NBit1"], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._callable.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._callable.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._callable.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._callable.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._callable.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._callable.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_hidden": true, "module_public": false}, "bool_": {".class": "SymbolTableNode", "cross_ref": "numpy.bool_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "complex128": {".class": "SymbolTableNode", "cross_ref": "numpy.complex128", "kind": "Gdef", "module_hidden": true, "module_public": false}, "complexfloating": {".class": "SymbolTableNode", "cross_ref": "numpy.complexfloating", "kind": "Gdef", "module_hidden": true, "module_public": false}, "dtype": {".class": "SymbolTableNode", "cross_ref": "numpy.dtype", "kind": "Gdef", "module_hidden": true, "module_public": false}, "float64": {".class": "SymbolTableNode", "cross_ref": "numpy.float64", "kind": "Gdef", "module_hidden": true, "module_public": false}, "floating": {".class": "SymbolTableNode", "cross_ref": "numpy.floating", "kind": "Gdef", "module_hidden": true, "module_public": false}, "generic": {".class": "SymbolTableNode", "cross_ref": "numpy.generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "int8": {".class": "SymbolTableNode", "cross_ref": "numpy.int8", "kind": "Gdef", "module_hidden": true, "module_public": false}, "int_": {".class": "SymbolTableNode", "cross_ref": "numpy.int_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "integer": {".class": "SymbolTableNode", "cross_ref": "numpy.integer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ndarray": {".class": "SymbolTableNode", "cross_ref": "numpy.n<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "number": {".class": "SymbolTableNode", "cross_ref": "numpy.number", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "signedinteger": {".class": "SymbolTableNode", "cross_ref": "numpy.<PERSON><PERSON><PERSON>r", "kind": "Gdef", "module_hidden": true, "module_public": false}, "timedelta64": {".class": "SymbolTableNode", "cross_ref": "numpy.timedelta64", "kind": "Gdef", "module_hidden": true, "module_public": false}, "unsignedinteger": {".class": "SymbolTableNode", "cross_ref": "numpy.unsignedinteger", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\AnacondaPath\\Lib\\site-packages\\numpy\\_typing\\_callable.pyi"}