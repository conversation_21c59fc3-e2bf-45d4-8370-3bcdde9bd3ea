{"data_mtime": 1749245427, "dep_lines": [3, 5, 6, 10, 11, 12, 14, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["numpy.random._generator", "numpy.random._mt19937", "numpy.random._pcg64", "numpy.random._philox", "numpy.random._sfc64", "numpy.random.bit_generator", "numpy.random.mtrand", "numpy._pytesttester", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "83c5adc54f5c7926f16eed2149a950da29192218", "id": "numpy.random", "ignore_all": true, "interface_hash": "1a5357aa53095692a012c2b4d6a45cc6b67742d2", "mtime": 1707226024, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\AnacondaPath\\Lib\\site-packages\\numpy\\random\\__init__.pyi", "plugin_data": null, "size": 2143, "suppressed": [], "version_id": "1.15.0"}